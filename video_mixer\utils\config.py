#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
Configuration Management Module
"""

import os
import json
from typing import Dict, Any


class Config:
    """配置管理类"""
    
    def __init__(self):
        self.config_dir = os.path.expanduser("~/.video_mixer")
        self.config_file = os.path.join(self.config_dir, "config.json")
        self.default_config = {
            # 应用程序配置
            "app": {
                "version": "1.0.0",
                "name": "视频混剪工具-裕页",
            },
            
            # 视频处理参数
            "video": {
                "min_speed": 0.9,
                "max_speed": 1.1,
                "max_scale": 1.1,
                "repeat_scale": 1,
                "width": 1080,
                "height": 1440,
                "fps": 30,
                "video_sizes": ["1080:1440", "1080:1920"]
            },
            
            # 字幕设置
            "subtitle": {
                "font_family": "",
                "font_size": 11,
                "bottom_margin": 50,
                "color": "#ffffff",
                "outline_color": "#000000",
                "outline_size": 1
            },
            
            # 用户设置
            "user": {
                "device_id": "",
                "secret": "",
                "saved_card_key": "",  # 保存的卡密（编码后）
                "last_video_path": "",
                "last_subtitle_path": "",
                "last_audio_path": "",
                "last_export_path": ""
            },
            
            # FFmpeg设置
            "ffmpeg": {
                "preset": "medium",
                "crf": 12,
                "maxrate": "5000k",
                "bufsize": "10000k",
                "audio_bitrate": "192k"
            }
        }
        
        self._config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 确保配置目录存在
            os.makedirs(self.config_dir, exist_ok=True)
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置和用户配置
                    return self._merge_config(self.default_config, config)
            else:
                # 创建默认配置文件
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None):
        """保存配置文件"""
        try:
            if config is None:
                config = self._config
                
            os.makedirs(self.config_dir, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置（递归）"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key_path: str, default=None):
        """获取配置值（支持点分隔的路径）"""
        try:
            keys = key_path.split('.')
            value = self._config
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value):
        """设置配置值（支持点分隔的路径）"""
        try:
            keys = key_path.split('.')
            config = self._config
            
            # 导航到最后一级的父级
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
            # 保存配置
            self.save_config()
            
        except Exception as e:
            print(f"设置配置值失败: {e}")
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用程序配置"""
        return self._config.get("app", {})
    
    def get_video_config(self) -> Dict[str, Any]:
        """获取视频配置"""
        return self._config.get("video", {})
    
    def get_subtitle_config(self) -> Dict[str, Any]:
        """获取字幕配置"""
        return self._config.get("subtitle", {})
    
    def get_user_config(self) -> Dict[str, Any]:
        """获取用户配置"""
        return self._config.get("user", {})
    
    def get_ffmpeg_config(self) -> Dict[str, Any]:
        """获取FFmpeg配置"""
        return self._config.get("ffmpeg", {})
    
    def update_user_paths(self, **paths):
        """更新用户路径配置"""
        user_config = self._config.setdefault("user", {})
        for key, value in paths.items():
            if value:  # 只更新非空值
                user_config[key] = value
        self.save_config()

    def save_card_key(self, card_key: str):
        """保存卡密到配置文件（编码后）"""
        try:
            import base64
            # 简单编码卡密
            encoded_key = base64.b64encode(card_key.encode('utf-8')).decode('utf-8')
            self.set('user.saved_card_key', encoded_key)
        except Exception as e:
            print(f"保存卡密失败: {e}")

    def load_saved_card_key(self) -> str:
        """加载保存的卡密"""
        try:
            import base64
            encoded_key = self.get('user.saved_card_key', '')
            if encoded_key:
                # 解码卡密
                card_key = base64.b64decode(encoded_key.encode('utf-8')).decode('utf-8')
                return card_key
            return ''
        except Exception as e:
            print(f"加载卡密失败: {e}")
            return ''


# 测试代码
if __name__ == "__main__":
    config = Config()
    print("配置加载完成")
    print(f"应用名称: {config.get('app.name')}")
    print(f"视频宽度: {config.get('video.width')}")
    
    # 测试设置配置
    config.set('user.device_id', 'test_device_id')
    print(f"设备ID: {config.get('user.device_id')}")
