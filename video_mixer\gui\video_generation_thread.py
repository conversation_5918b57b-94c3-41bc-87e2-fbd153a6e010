#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成线程
Video Generation Thread
"""

import os
import tempfile
import shutil
from datetime import datetime
from typing import List, Dict, Any
from PyQt5.QtCore import QThread, pyqtSignal

try:
    from ..core.video_processor import VideoProcessor, ProcessingParams
    from ..core.algorithm import MatchResult
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.video_processor import VideoProcessor, ProcessingParams
    from core.algorithm import MatchResult


class VideoGenerationThread(QThread):
    """视频生成线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度百分比, 状态消息
    video_completed = pyqtSignal(int, str)   # 视频序号, 输出路径
    generation_finished = pyqtSignal(bool, str)  # 是否成功, 消息
    log_message = pyqtSignal(str)  # 日志消息
    
    def __init__(
        self,
        match_results: List[List[MatchResult]],
        audio_path: str,
        subtitle_path: str,
        export_path: str,
        subtitle_style: Dict[str, Any],
        audio_duration: float
    ):
        super().__init__()
        self.match_results = match_results
        self.audio_path = audio_path
        self.subtitle_path = subtitle_path
        self.export_path = export_path
        self.subtitle_style = subtitle_style
        self.audio_duration = audio_duration
        
        self.video_processor = VideoProcessor()
        self.is_cancelled = False
        self.temp_dirs = []
    
    def cancel(self):
        """取消生成"""
        self.is_cancelled = True
        self.log_message.emit("正在取消视频生成...")
    
    def run(self):
        """运行线程"""
        try:
            self.log_message.emit("开始生成视频...")
            
            # 创建输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(self.export_path, f"生成视频_{timestamp}")
            os.makedirs(output_dir, exist_ok=True)
            
            total_videos = len(self.match_results)
            self.log_message.emit(f"🔍 开始生成，match_results长度: {total_videos}")

            successful_videos = 0
            for video_index, video_matches in enumerate(self.match_results):
                if self.is_cancelled:
                    break
                
                try:
                    # 更新进度
                    progress = int((video_index / total_videos) * 100)
                    self.progress_updated.emit(progress, f"正在生成第 {video_index + 1} 个视频...")
                    
                    # 生成单个视频
                    output_path = os.path.join(output_dir, f"{video_index + 1}.mp4")
                    success = self.generate_single_video(video_matches, output_path, video_index + 1)
                    
                    if success:
                        successful_videos += 1
                        self.video_completed.emit(video_index + 1, output_path)
                        self.log_message.emit(f"第 {video_index + 1} 个视频生成完成")
                    else:
                        self.log_message.emit(f"第 {video_index + 1} 个视频生成失败")
                        
                except Exception as e:
                    self.log_message.emit(f"生成第 {video_index + 1} 个视频时出错: {e}")
                    continue
            
            # 清理临时文件
            self.cleanup_temp_dirs()
            
            if self.is_cancelled:
                self.generation_finished.emit(False, "视频生成已取消")
            else:
                self.progress_updated.emit(100, "视频生成完成")
                self.log_message.emit(f"🔍 生成统计: 计划{total_videos}个，成功{successful_videos}个")
                if successful_videos != total_videos:
                    self.log_message.emit(f"⚠️ 生成数量不匹配！计划{total_videos}个，实际成功{successful_videos}个")
                self.generation_finished.emit(True, f"成功生成 {successful_videos} 个视频到: {output_dir}")
                
        except Exception as e:
            self.cleanup_temp_dirs()
            self.generation_finished.emit(False, f"视频生成失败: {e}")
    
    def generate_single_video(self, video_matches: List[MatchResult], output_path: str, video_number: int) -> bool:
        """生成单个视频"""
        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp(prefix=f"video_mixer_{video_number}_")
            self.temp_dirs.append(temp_dir)
            
            segment_paths = []
            
            # 处理每个视频片段
            for segment_index, match_result in enumerate(video_matches):
                if self.is_cancelled:
                    return False
                
                # 构建处理参数
                # 计算播放速度以实现时长调整
                original_duration = match_result.subtitle.duration_ms / 1000.0

                if match_result.adjusted_duration is not None:
                    # 通过调整播放速度来实现时长变化
                    # 如果目标时长更短，需要加速播放；如果更长，需要减速播放
                    speed_adjustment = original_duration / match_result.adjusted_duration
                    final_speed_scale = match_result.video_candidate.speed_scale * speed_adjustment
                    final_duration = match_result.adjusted_duration
                else:
                    final_speed_scale = match_result.video_candidate.speed_scale
                    final_duration = original_duration

                params = ProcessingParams(
                    speed_scale=final_speed_scale,
                    size_scale=match_result.video_candidate.size_scale,
                    start_time=match_result.start_offset,
                    duration=final_duration,
                    output_width=1080,  # 从配置获取
                    output_height=1440  # 从配置获取
                )
                
                # 输出路径
                segment_path = os.path.join(temp_dir, f"segment_{segment_index + 1}.mp4")
                segment_paths.append(segment_path)
                
                # 处理视频片段
                success = self.video_processor.process_video_segment(
                    match_result.video_candidate.video_info.path,
                    segment_path,
                    params,
                    progress_callback=None  # 简化处理，不显示详细进度
                )
                
                if not success:
                    self.log_message.emit(f"处理视频片段 {segment_index + 1} 失败")
                    return False
            
            if self.is_cancelled:
                return False
            
            # 合并视频片段
            success = self.video_processor.merge_video_segments(
                segment_paths,
                self.audio_path,
                self.subtitle_path,
                output_path,
                self.subtitle_style,
                self.audio_duration,
                progress_callback=None  # 简化处理
            )
            
            return success
            
        except Exception as e:
            self.log_message.emit(f"生成单个视频失败: {e}")
            return False
    
    def cleanup_temp_dirs(self):
        """清理临时目录"""
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception as e:
                self.log_message.emit(f"清理临时目录失败: {e}")
        
        self.temp_dirs.clear()
        
        # 清理视频处理器的临时目录
        try:
            self.video_processor.cleanup_temp_dir()
        except Exception as e:
            self.log_message.emit(f"清理视频处理器临时目录失败: {e}")


# 测试代码
if __name__ == "__main__":
    print("视频生成线程模块")
