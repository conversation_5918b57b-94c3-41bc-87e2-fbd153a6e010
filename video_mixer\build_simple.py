#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的PyInstaller打包脚本 - 解决兼容性问题
"""

import os
import sys
import subprocess
import shutil

def main():
    """主打包函数"""
    print("🚀 开始简化打包...")
    
    # 清理旧文件
    print("🧹 清理旧文件...")
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除: {dir_name}")
    
    # 构建命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--name=视频混剪工具-裕页',
        '--windowed',  # 不显示控制台
        '--onedir',    # 单目录模式
        '--clean',     # 清理缓存
        '--noconfirm', # 不询问覆盖
        
        # 图标
        '--icon=1.ico',
        
        # 添加数据文件
        '--add-data=1.ico;.',
        
        # 关键的隐藏导入 - 只包含必要的
        '--hidden-import=gui.main_window',
        '--hidden-import=gui.login_dialog',
        '--hidden-import=gui.video_generation_thread',
        '--hidden-import=core.subtitle_parser',
        '--hidden-import=core.video_processor',
        '--hidden-import=core.algorithm',
        '--hidden-import=core.auth_manager',
        '--hidden-import=utils.config',
        '--hidden-import=utils.device_id',
        
        # PyQt5相关
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        
        # 其他必要库
        '--hidden-import=requests',
        '--hidden-import=numpy',
        '--hidden-import=PIL',
        '--hidden-import=pysrt',
        '--hidden-import=psutil',
        
        # 排除不需要的模块
        '--exclude-module=tkinter',
        '--exclude-module=matplotlib',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--exclude-module=jupyter',
        '--exclude-module=IPython',
        
        'main.py'
    ]
    
    print("🔨 执行打包命令...")
    print(f"命令: {' '.join(cmd[:5])} ... (共{len(cmd)}个参数)")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        
        # 创建使用说明
        dist_dir = "dist/视频混剪工具-裕页"
        if os.path.exists(dist_dir):
            with open(f"{dist_dir}/使用说明.txt", 'w', encoding='utf-8') as f:
                f.write("""视频混剪工具-裕页 v1.0.0

使用说明：
1. 双击"视频混剪工具-裕页.exe"启动程序
2. 首次使用需要输入卡密进行验证
3. 卡密会自动保存，下次启动无需重新输入

系统要求：
- Windows 7/8/10/11 (推荐64位)
- 至少2GB内存
- 至少1GB可用磁盘空间

注意事项：
- 请保持文件夹完整，不要删除任何文件
- 可以将整个文件夹复制到其他电脑使用
- 如遇到问题，请联系技术支持

更新日志 v1.0.0：
- ✅ 修复了FFmpeg运行时的小黑框闪烁问题
- ✅ 修复了数据库连接失败导致程序崩溃的问题
- ✅ 优化了打包兼容性，支持更多电脑环境

技术支持：裕页
""")
            print("📝 创建使用说明完成")
        
        print("🎉 打包完成!")
        print(f"输出目录: {dist_dir}")
        print(f"可执行文件: {dist_dir}/视频混剪工具-裕页.exe")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败!")
        print(f"错误信息: {e.stderr}")
        return False

if __name__ == "__main__":
    if not os.path.exists("main.py"):
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    success = main()
    if not success:
        sys.exit(1)
    
    input("\n按回车键退出...")
