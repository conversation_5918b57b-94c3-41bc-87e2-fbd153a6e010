#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原增强视频生成算法（极严格版本）
基于纯素材重叠检测，最严格的去重标准
"""

import random
from typing import List, Tuple, Dict, Set
from dataclasses import dataclass
from .algorithm import OptimizedVideoGenerator, GeneratedVideo, VideoSegment

@dataclass
class OriginalDuplicateDetectionConfig:
    """原增强算法重复检测配置"""
    enable_material_overlap_check: bool = True  # 素材重叠检测
    material_overlap_threshold: float = 0.25 # 素材重叠阈值（25%）

@dataclass
class OriginalDuplicateCheckResult:
    """原增强算法重复检测结果"""
    is_duplicate: bool
    duplicate_type: str
    duplicate_rate: float
    details: Dict[str, float]

class OriginalEnhancedVideoGenerator(OptimizedVideoGenerator):
    """原增强视频生成器（极严格版本）"""
    
    def __init__(self, num_materials: int = 100, segments_per_video: int = 4, 
                 enable_similarity_check: bool = False, optimization_mode: str = "balanced"):
        super().__init__(num_materials, segments_per_video, enable_similarity_check, optimization_mode)
        
        # 原增强算法配置
        self.detection_config = OriginalDuplicateDetectionConfig()
        self._adjust_config_for_original_mode()
        self.generation_stats = {
            'total_attempts': 0,
            'rejected_by_material_overlap': 0,
            'accepted': 0
        }
        
        print("🛡️ 原增强算法已启用（极严格模式）")
        print("📊 极严格特性:")
        print("   - 纯素材重叠率控制")
        print("   - 不考虑时间位置差异")
        print(f"   - 重叠率限制: ≤{self.detection_config.material_overlap_threshold*100:.0f}%")
        print("   - 最高质量保证")
    
    def _adjust_config_for_original_mode(self):
        """调整配置为原增强模式"""
        # 根据素材数量调整重叠阈值
        if self.num_materials >= 50:
            # 大量素材：严格控制
            self.detection_config.material_overlap_threshold = 0.24  # 24%重叠限制
        elif self.num_materials >= 30:
            # 中等素材：标准阈值
            self.detection_config.material_overlap_threshold = 0.25 # 25%重叠限制
        else:
            # 少量素材：稍微宽松
            self.detection_config.material_overlap_threshold = 0.30  # 30%重叠限制
        
        print(f"📊 原增强重叠阈值: {self.detection_config.material_overlap_threshold*100:.0f}%")
    
    def calculate_original_material_overlap_rate(self, video1: GeneratedVideo, video2: GeneratedVideo) -> float:
        """计算原版素材重叠率（纯素材集合比较）"""
        materials1 = set(seg.video_index for seg in video1.segments)
        materials2 = set(seg.video_index for seg in video2.segments)
        
        if not materials1 or not materials2:
            return 0.0
        
        # 计算共同使用的素材数量
        common_materials = materials1.intersection(materials2)
        
        # 重叠率 = 共同素材数 / 每个视频的素材数
        overlap_rate = len(common_materials) / len(materials1)
        
        return overlap_rate
    
    def original_enhanced_duplicate_detection(self, candidate_video: GeneratedVideo, 
                                            existing_videos: List[GeneratedVideo]) -> OriginalDuplicateCheckResult:
        """原增强重复检测（极严格）"""
        self.generation_stats['total_attempts'] += 1
        
        max_overlap_rate = 0.0
        duplicate_details = {}
        
        for existing_video in existing_videos:
            # 计算纯素材重叠率
            overlap_rate = self.calculate_original_material_overlap_rate(candidate_video, existing_video)

            duplicate_details['material_overlap_rate'] = max(duplicate_details.get('material_overlap_rate', 0), overlap_rate)
            
            # 如果重叠率超过阈值，拒绝
            if overlap_rate > self.detection_config.material_overlap_threshold:
                self.generation_stats['rejected_by_material_overlap'] += 1
                return OriginalDuplicateCheckResult(
                    is_duplicate=True,
                    duplicate_type="HIGH_MATERIAL_OVERLAP",
                    duplicate_rate=overlap_rate,
                    details=duplicate_details
                )
            
            max_overlap_rate = max(max_overlap_rate, overlap_rate)
        
        # 通过所有检测
        self.generation_stats['accepted'] += 1
        return OriginalDuplicateCheckResult(
            is_duplicate=False,
            duplicate_type="ACCEPTABLE",
            duplicate_rate=max_overlap_rate,
            details=duplicate_details
        )
    
    def is_video_valid_original_enhanced(self, candidate_video: GeneratedVideo, 
                                       existing_videos: List[GeneratedVideo]) -> bool:
        """原增强的视频有效性检查"""
        result = self.original_enhanced_duplicate_detection(candidate_video, existing_videos)
        return not result.is_duplicate
    
    def generate_optimal_videos_original_enhanced(self, max_videos: int = 200) -> Tuple[int, List[GeneratedVideo]]:
        """使用原增强算法生成视频"""
        print(f"🛡️ 开始原增强生成，目标: {max_videos}个视频")
        
        # 重置统计
        self.generation_stats = {key: 0 for key in self.generation_stats}
        
        generated_videos = []
        max_attempts = max_videos * 150  # 增加尝试次数
        consecutive_failures = 0
        max_consecutive_failures = 300
        
        for attempt in range(max_attempts):
            if len(generated_videos) >= max_videos:
                break
            
            # 生成候选视频
            candidate_video = self._generate_single_video_candidate_original()
            if not candidate_video:
                consecutive_failures += 1
                continue
            
            # 使用原增强检测
            if self.is_video_valid_original_enhanced(candidate_video, generated_videos):
                generated_videos.append(candidate_video)
                consecutive_failures = 0
                
                if len(generated_videos) % 5 == 0:
                    self._print_original_enhanced_progress(len(generated_videos), max_videos)
            else:
                consecutive_failures += 1
                
                if consecutive_failures >= max_consecutive_failures:
                    print(f"⚠️ 连续失败 {max_consecutive_failures} 次，停止生成")
                    break
        
        self._print_original_enhanced_summary(len(generated_videos), max_videos)
        return len(generated_videos), generated_videos
    
    def _generate_single_video_candidate_original(self) -> GeneratedVideo:
        """生成单个候选视频（原版）"""
        try:
            import random
            video_segments = []
            
            # 随机选择素材，允许重复使用
            selected_materials = random.sample(range(self.num_materials), self.segments_per_video)
            
            for position in range(self.segments_per_video):
                # 创建片段
                segment = VideoSegment(
                    video_index=selected_materials[position],
                    position=position,
                    start_time=0.0,
                    duration=3.0  # 默认时长
                )
                video_segments.append(segment)
            
            if len(video_segments) == self.segments_per_video:
                video_id = f"original_enhanced_{random.randint(1000, 9999)}"
                return GeneratedVideo(segments=video_segments, video_id=video_id)
            
        except Exception as e:
            print(f"❌ 生成候选视频失败: {e}")
        
        return None
    
    def _print_original_enhanced_progress(self, current: int, target: int):
        """打印原增强算法进度"""
        progress = current / target * 100
        print(f"🛡️ 原增强进度 {progress:.1f}%: 已生成 {current}/{target} 个视频")
    
    def _print_original_enhanced_summary(self, generated: int, target: int):
        """打印原增强算法总结"""
        stats = self.generation_stats
        total_attempts = stats['total_attempts']
        
        print(f"\n🛡️ 原增强算法完成！")
        print(f"📊 生成统计:")
        print(f"   - 目标视频: {target} 个")
        print(f"   - 成功生成: {generated} 个")
        print(f"   - 总尝试次数: {total_attempts}")
        print(f"   - 成功率: {generated/total_attempts*100:.1f}%" if total_attempts > 0 else "")
        
        print(f"\n🔍 拒绝原因分析:")
        print(f"   - 素材重叠: {stats['rejected_by_material_overlap']} 次")
        print(f"   - 通过检测: {stats['accepted']} 次")
        
        efficiency = generated / total_attempts * 100 if total_attempts > 0 else 0
        print(f"\n⚡ 算法效率: {efficiency:.2f}%")
        
        if efficiency < 3:
            print("💡 建议: 增加素材数量或使用融合版增强算法")
        elif efficiency > 10:
            print("🎉 原增强算法效率优秀！")
    
    def generate_optimal_videos_with_history(self, max_videos: int = 200) -> Tuple[int, List]:
        """基于历史记录的原增强算法生成"""
        print(f"🚀 开始历史感知原增强生成，目标: {max_videos}个视频")

        try:
            # 导入历史感知功能
            from .video_history_database import VideoHistoryDB, CombinationSequence

            # 创建数据库连接
            db = VideoHistoryDB()
            if not db.connect():
                print("⚠️ 数据库连接失败，使用普通原增强算法")
                return self.generate_optimal_videos_original_enhanced(max_videos)

            print("✅ 历史感知数据库连接成功")

            # 使用原增强算法生成
            count, generated_videos = self.generate_optimal_videos_original_enhanced(max_videos)

            # 过滤历史重复
            filtered_videos = []
            skipped_count = 0

            for video in generated_videos:
                # 创建组合序列
                material_indices = [seg.video_index for seg in video.segments]
                positions = [seg.position for seg in video.segments]

                combination = CombinationSequence(
                    material_indices=material_indices,
                    positions=positions
                )

                # 检查是否已存在
                if db.is_combination_used(combination):
                    skipped_count += 1
                    print(f"🔍 历史检查：组合已存在，跳过 - 哈希: {combination.get_hash()[:8]}")
                    continue

                filtered_videos.append(video)

            print(f"📊 跳过历史重复: {skipped_count} 个")
            print(f"✅ 历史感知过滤后: {len(filtered_videos)} 个视频")

            db.close()
            return len(filtered_videos), filtered_videos

        except Exception as e:
            print(f"❌ 历史感知处理失败: {e}")
            # 回退到普通原增强算法
            return self.generate_optimal_videos_original_enhanced(max_videos)

    def get_original_enhanced_stats(self) -> dict:
        """获取原增强算法统计信息"""
        return {
            'generation_stats': self.generation_stats.copy(),
            'detection_config': {
                'material_overlap_threshold': self.detection_config.material_overlap_threshold,
                'enable_material_overlap_check': self.detection_config.enable_material_overlap_check
            }
        }
