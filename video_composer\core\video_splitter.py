"""
视频分割器 - 提取自video_qiege项目
"""
import os
import subprocess
import json
import re
from typing import List, Dict, Optional

class VideoSplitter:
    """精简版视频分割器"""

    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe",
                 scene_threshold: float = 1, min_segment_duration: float = 1.0,
                 fallback_segment_duration: float = 3.0):
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.scene_threshold = scene_threshold  # 场景检测阈值
        self.min_segment_duration = min_segment_duration  # 最短片段时长
        self.fallback_segment_duration = fallback_segment_duration  # 备用分割间隔
    
    def get_video_info(self, video_path: str) -> Dict:
        """获取视频基本信息"""
        cmd = [
            self.ffprobe_path, "-v", "quiet", "-print_format", "json",
            "-show_format", "-show_streams", video_path
        ]

        # 尝试多种编码方式
        for encoding in ['utf-8', 'gbk', 'cp1252']:
            try:
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'encoding': encoding,
                    'errors': 'ignore',
                    'timeout': 30
                }

                # 在Windows下隐藏命令窗口
                if os.name == 'nt':
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(cmd, **kwargs)

                if result.returncode != 0:
                    continue

                if not result.stdout or result.stdout.strip() == "":
                    continue

                info = json.loads(result.stdout)
                video_stream = next((s for s in info['streams'] if s['codec_type'] == 'video'), None)

                if not video_stream:
                    raise RuntimeError("未找到视频流")

                return {
                    'duration': float(info['format']['duration']),
                    'width': int(video_stream['width']),
                    'height': int(video_stream['height']),
                    'path': video_path
                }

            except (subprocess.TimeoutExpired, json.JSONDecodeError, UnicodeDecodeError, ValueError) as e:
                print(f"编码 {encoding} 解析失败: {str(e)}")
                continue

        raise RuntimeError(f"获取视频信息失败，所有编码尝试都失败: {video_path}")
    
    def detect_scenes(self, video_path: str, threshold: float = None) -> List[float]:
        """检测场景变化点"""
        if threshold is None:
            threshold = self.scene_threshold
        ffmpeg_threshold = threshold / 10.0

        cmd = [
            self.ffmpeg_path, "-i", video_path,
            "-filter_complex", f"select='gt(scene,{ffmpeg_threshold})',showinfo",
            "-f", "null", "-"
        ]

        # 尝试多种编码方式
        time_points = [0.0]  # 开始点

        for encoding in ['utf-8', 'gbk', 'cp1252']:
            try:
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'encoding': encoding,
                    'errors': 'ignore',
                    'timeout': 60
                }

                # 在Windows下隐藏命令窗口
                if os.name == 'nt':
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                process = subprocess.run(cmd, **kwargs)

                # 解析时间点
                if process.stderr:
                    pattern = r'pts_time:(\d+\.?\d*)'
                    matches = re.findall(pattern, process.stderr)

                    for match in matches:
                        try:
                            time_points.append(float(match))
                        except ValueError:
                            continue

                # 如果成功解析，跳出循环
                break

            except (subprocess.TimeoutExpired, UnicodeDecodeError) as e:
                print(f"编码 {encoding} 场景检测失败: {str(e)}")
                continue

        # 添加结束点
        video_info = self.get_video_info(video_path)
        time_points.append(video_info['duration'])

        # 如果没有检测到场景变化，按固定间隔分割
        if len(time_points) <= 2:
            duration = video_info['duration']
            segment_duration = self.fallback_segment_duration  # 使用配置的间隔
            time_points = [i * segment_duration for i in range(int(duration / segment_duration) + 1)]
            time_points.append(duration)

        return sorted(set(time_points))
    
    def split_video(self, video_path: str, output_dir: str, prefix: str) -> List[Dict]:
        """分割视频并返回片段信息"""
        os.makedirs(output_dir, exist_ok=True)

        time_points = self.detect_scenes(video_path)
        segments = []

        for i in range(len(time_points) - 1):
            start_time = time_points[i]
            duration = time_points[i + 1] - start_time

            if duration < self.min_segment_duration:  # 跳过过短片段
                continue

            output_path = os.path.join(output_dir, f"{prefix}-{len(segments) + 1}.mp4")

            cmd = [
                self.ffmpeg_path, "-i", video_path,
                "-ss", str(start_time), "-t", str(duration),
                "-c:v", "libx264", "-c:a", "aac", "-y", output_path
            ]

            # 使用正确的编码设置
            kwargs = {
                'capture_output': True,
                'text': True,
                'encoding': 'utf-8',
                'errors': 'ignore',
                'timeout': 300
            }

            # 在Windows下隐藏命令窗口
            if os.name == 'nt':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

            try:
                result = subprocess.run(cmd, **kwargs)

                if result.returncode == 0 and os.path.exists(output_path):
                    segments.append({
                        'path': output_path,
                        'start_time': start_time,
                        'duration': duration,
                        'index': len(segments) + 1,
                        'source': prefix
                    })
                    print(f"成功分割片段: {output_path}")
                else:
                    print(f"分割失败: {result.stderr}")

            except (subprocess.TimeoutExpired, UnicodeDecodeError) as e:
                print(f"分割片段时出错: {str(e)}")
                continue

        return segments
