"""
视频分割器 - 提取自video_qiege项目
"""
import os
import subprocess
import json
import re
from typing import List, Dict, Optional

class VideoSplitter:
    """精简版视频分割器"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
    
    def get_video_info(self, video_path: str) -> Dict:
        """获取视频基本信息"""
        cmd = [
            self.ffprobe_path, "-v", "quiet", "-print_format", "json",
            "-show_format", "-show_streams", video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise RuntimeError(f"获取视频信息失败: {result.stderr}")
        
        info = json.loads(result.stdout)
        video_stream = next((s for s in info['streams'] if s['codec_type'] == 'video'), None)
        
        if not video_stream:
            raise RuntimeError("未找到视频流")
        
        return {
            'duration': float(info['format']['duration']),
            'width': int(video_stream['width']),
            'height': int(video_stream['height']),
            'path': video_path
        }
    
    def detect_scenes(self, video_path: str, threshold: float = 0.1) -> List[float]:
        """检测场景变化点"""
        ffmpeg_threshold = threshold / 10.0
        
        cmd = [
            self.ffmpeg_path, "-i", video_path,
            "-filter_complex", f"select='gt(scene,{ffmpeg_threshold})',showinfo",
            "-f", "null", "-"
        ]
        
        process = subprocess.run(cmd, capture_output=True, text=True, 
                               creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        
        # 解析时间点 - 修复正则表达式
        time_points = [0.0]  # 开始点
        
        if process.stderr:
            pattern = r'pts_time:(\d+\.?\d*)'
            matches = re.findall(pattern, process.stderr)
            
            for match in matches:
                try:
                    time_points.append(float(match))
                except ValueError:
                    continue
        
        # 添加结束点
        video_info = self.get_video_info(video_path)
        time_points.append(video_info['duration'])
        
        # 如果没有检测到场景变化，按固定间隔分割
        if len(time_points) <= 2:
            duration = video_info['duration']
            segment_duration = 10.0  # 每10秒一个片段
            time_points = [i * segment_duration for i in range(int(duration / segment_duration) + 1)]
            time_points.append(duration)
        
        return sorted(set(time_points))
    
    def split_video(self, video_path: str, output_dir: str, prefix: str) -> List[Dict]:
        """分割视频并返回片段信息"""
        os.makedirs(output_dir, exist_ok=True)
        
        time_points = self.detect_scenes(video_path)
        segments = []
        
        for i in range(len(time_points) - 1):
            start_time = time_points[i]
            duration = time_points[i + 1] - start_time
            
            if duration < 1.0:  # 跳过过短片段
                continue
            
            output_path = os.path.join(output_dir, f"{prefix}-{len(segments) + 1}.mp4")
            
            cmd = [
                self.ffmpeg_path, "-i", video_path,
                "-ss", str(start_time), "-t", str(duration),
                "-c:v", "libx264", "-c:a", "aac", "-y", output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
            
            if result.returncode == 0:
                segments.append({
                    'path': output_path,
                    'start_time': start_time,
                    'duration': duration,
                    'index': len(segments) + 1,
                    'source': prefix
                })
        
        return segments
