"""
主窗口界面
"""
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.video_splitter import VideoSplitter
from core.video_composer import VideoComposer
from core.smart_selector import SmartSelector

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频智能合成工具")
        self.setGeometry(100, 100, 800, 600)
        
        self.video_paths = []
        self.segments = []
        
        self.init_ui()
        
        # 初始化核心组件
        self.splitter = VideoSplitter()
        self.composer = VideoComposer()
        self.selector = SmartSelector()
    
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 视频选择区域
        video_group = QGroupBox("视频选择")
        video_layout = QVBoxLayout(video_group)
        
        self.video_list = QListWidget()
        video_layout.addWidget(self.video_list)
        
        btn_layout = QHBoxLayout()
        self.btn_add_video = QPushButton("添加视频")
        self.btn_clear_videos = QPushButton("清空")
        btn_layout.addWidget(self.btn_add_video)
        btn_layout.addWidget(self.btn_clear_videos)
        video_layout.addLayout(btn_layout)
        
        layout.addWidget(video_group)
        
        # 设置区域
        settings_group = QGroupBox("合成设置")
        settings_layout = QFormLayout(settings_group)
        
        self.subtitle_path = QLineEdit()
        self.btn_select_subtitle = QPushButton("选择字幕")
        subtitle_layout = QHBoxLayout()
        subtitle_layout.addWidget(self.subtitle_path)
        subtitle_layout.addWidget(self.btn_select_subtitle)
        settings_layout.addRow("字幕文件:", subtitle_layout)
        
        self.output_path = QLineEdit()
        self.btn_select_output = QPushButton("选择输出")
        output_layout = QHBoxLayout()
        output_layout.addWidget(self.output_path)
        output_layout.addWidget(self.btn_select_output)
        settings_layout.addRow("输出路径:", output_layout)
        
        layout.addWidget(settings_group)
        
        # 操作按钮
        self.btn_start = QPushButton("开始合成")
        self.btn_start.setMinimumHeight(40)
        layout.addWidget(self.btn_start)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        layout.addWidget(self.log_text)
        
        # 连接信号
        self.btn_add_video.clicked.connect(self.add_video)
        self.btn_clear_videos.clicked.connect(self.clear_videos)
        self.btn_select_subtitle.clicked.connect(self.select_subtitle)
        self.btn_select_output.clicked.connect(self.select_output)
        self.btn_start.clicked.connect(self.start_compose)
    
    def add_video(self):
        files, _ = QFileDialog.getOpenFileNames(self, "选择视频文件", "", "视频文件 (*.mp4 *.avi *.mov)")
        for file in files:
            if len(self.video_paths) < 3:
                self.video_paths.append(file)
                self.video_list.addItem(os.path.basename(file))
    
    def clear_videos(self):
        self.video_paths.clear()
        self.video_list.clear()
    
    def select_subtitle(self):
        file, _ = QFileDialog.getOpenFileName(self, "选择字幕文件", "", "字幕文件 (*.srt *.ass)")
        if file:
            self.subtitle_path.setText(file)
    
    def select_output(self):
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_path.setText(directory)
    
    def start_compose(self):
        if len(self.video_paths) != 3:
            QMessageBox.warning(self, "警告", "请选择3个视频文件")
            return
        
        if not self.subtitle_path.text():
            QMessageBox.warning(self, "警告", "请选择字幕文件")
            return
            
        if not self.output_path.text():
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return
        
        # 生成输出文件路径
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(self.output_path.text(), f"合成视频_{timestamp}.mp4")
        
        # 启动合成线程
        self.compose_thread = ComposeThread(
            self.video_paths, 
            self.subtitle_path.text(),
            output_file,
            self.splitter, self.composer, self.selector
        )
        self.compose_thread.log_signal.connect(self.log_text.append)
        self.compose_thread.start()

class ComposeThread(QThread):
    log_signal = pyqtSignal(str)
    
    def __init__(self, video_paths, subtitle_path, output_path, splitter, composer, selector):
        super().__init__()
        self.video_paths = video_paths
        self.subtitle_path = subtitle_path
        self.output_path = output_path
        self.splitter = splitter
        self.composer = composer
        self.selector = selector
    
    def run(self):
        try:
            # 1. 分割视频
            all_segments = []
            audio_durations = []
            
            for i, video_path in enumerate(self.video_paths):
                prefix = chr(65 + i)  # A, B, C
                self.log_signal.emit(f"正在分割视频 {prefix}...")
                
                segments = self.splitter.split_video(video_path, "temp_segments", prefix)
                all_segments.extend(segments)
                
                # 获取音频时长
                info = self.splitter.get_video_info(video_path)
                audio_durations.append((info['duration'], video_path))
            
            # 2. 选择最短音频
            shortest_audio = min(audio_durations, key=lambda x: x[0])
            self.log_signal.emit(f"选择最短音频: {shortest_audio[0]:.2f}秒")
            
            # 3. 智能选择片段
            selected_segments = self.selector.select_segments(all_segments, shortest_audio[0])
            self.log_signal.emit(f"选择了 {len(selected_segments)} 个片段")
            
            # 4. 合成视频
            self.log_signal.emit("开始合成视频...")
            segment_paths = [s['path'] for s in selected_segments]
            
            # 提取音频
            audio_path = "temp_audio.aac"
            self.composer.extract_audio(shortest_audio[1], audio_path)
            
            # 合成
            success = self.composer.merge_segments(
                segment_paths, audio_path, self.subtitle_path, self.output_path
            )
            
            if success:
                self.log_signal.emit("合成完成！")
            else:
                self.log_signal.emit("合成失败！")
                
        except Exception as e:
            self.log_signal.emit(f"错误: {str(e)}")

