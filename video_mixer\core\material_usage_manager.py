#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材使用管理器
Material Usage Manager
"""

import random
import os
import sys
from typing import List, Dict, Any, Tuple
from collections import defaultdict
from dataclasses import dataclass

try:
    from ..utils.logger import get_logger
except ImportError:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger

@dataclass
class MaterialInfo:
    """素材信息"""
    path: str
    name: str
    duration: float
    usage_count: int = 0
    weight: float = 1.0
    last_used_time: float = 0.0

class MaterialUsageManager:
    """素材使用管理器"""
    
    def __init__(self, debug_mode: bool = False):
        self.logger = get_logger()
        self.debug_mode = debug_mode
        
        # 素材使用统计
        self.material_stats = {}  # {material_path: MaterialInfo}
        self.selection_history = []  # 选择历史记录
        self.weight_history = []  # 权重变化历史
        
        # 统计信息
        self.total_selections = 0
        self.max_usage_count = 0
        
        self.logger.info("📊 素材使用管理器初始化完成")
    
    def register_materials(self, materials: List[Any]) -> None:
        """注册素材列表"""
        for material in materials:
            material_path = getattr(material, 'path', str(material))
            material_name = os.path.basename(material_path)
            material_duration = getattr(material, 'duration', 0.0)
            
            if material_path not in self.material_stats:
                self.material_stats[material_path] = MaterialInfo(
                    path=material_path,
                    name=material_name,
                    duration=material_duration,
                    usage_count=0,
                    weight=1.0,
                    last_used_time=0.0
                )
        
        self.logger.info(f"📊 已注册 {len(self.material_stats)} 个素材")
        if self.debug_mode:
            self.logger.debug(f"注册的素材: {list(self.material_stats.keys())}")
    
    def calculate_weights(self) -> Dict[str, float]:
        """计算所有素材的选择权重"""
        if not self.material_stats:
            return {}
        
        # 获取当前最大使用次数
        usage_counts = [info.usage_count for info in self.material_stats.values()]
        self.max_usage_count = max(usage_counts) if usage_counts else 0
        
        # 计算权重：差值权重公式
        weights = {}
        for path, info in self.material_stats.items():
            # 权重 = 最大使用次数 - 当前使用次数 + 1
            weight = self.max_usage_count - info.usage_count + 1
            weights[path] = weight
            info.weight = weight
        
        if self.debug_mode:
            self.logger.debug(f"权重计算完成，最大使用次数: {self.max_usage_count}")
        
        return weights
    
    def select_materials(self, materials: List[Any], count: int) -> List[Any]:
        """按权重选择指定数量的素材"""
        if not materials or count <= 0:
            return []

        # 只在第一次或素材数量变化时注册
        if len(self.material_stats) != len(materials):
            self.register_materials(materials)

        # 计算权重
        weights = self.calculate_weights()
        
        # 准备选择数据
        material_paths = [getattr(m, 'path', str(m)) for m in materials]
        material_weights = [weights.get(path, 1.0) for path in material_paths]
        
        # 执行加权随机选择
        selected_materials = self._weighted_random_choice(
            materials, material_weights, count
        )
        
        # 记录选择历史
        self._record_selection(materials, material_weights, selected_materials)
        
        return selected_materials
    
    def _weighted_random_choice(self, materials: List[Any], weights: List[float], count: int) -> List[Any]:
        """加权随机选择（避免重复）"""
        if count >= len(materials):
            return materials.copy()
        
        selected = []
        available_materials = materials.copy()
        available_weights = weights.copy()
        
        for _ in range(count):
            if not available_materials:
                break
            
            # 按权重随机选择一个
            try:
                chosen = random.choices(
                    available_materials,
                    weights=available_weights,
                    k=1
                )[0]
                
                selected.append(chosen)
                
                # 从可选列表中移除已选择的素材
                idx = available_materials.index(chosen)
                available_materials.pop(idx)
                available_weights.pop(idx)
                
            except (ValueError, IndexError) as e:
                self.logger.warning(f"素材选择异常: {e}")
                break
        
        return selected
    
    def update_usage(self, selected_materials: List[Any]) -> None:
        """更新素材使用统计"""
        import time
        current_time = time.time()
        
        for material in selected_materials:
            material_path = getattr(material, 'path', str(material))
            
            if material_path in self.material_stats:
                info = self.material_stats[material_path]
                info.usage_count += 1
                info.last_used_time = current_time
                
                if self.debug_mode:
                    self.logger.debug(f"更新素材使用: {info.name} -> {info.usage_count}次")
        
        self.total_selections += 1
        
        # 重新计算权重
        self.calculate_weights()
    
    def _record_selection(self, materials: List[Any], weights: List[float], selected: List[Any]) -> None:
        """记录选择历史"""
        selection_record = {
            'timestamp': self.total_selections,
            'available_count': len(materials),
            'selected_count': len(selected),
            'weights_sum': sum(weights),
            'selected_materials': [getattr(m, 'path', str(m)) for m in selected]
        }
        
        self.selection_history.append(selection_record)
        
        # 记录权重分布
        weight_distribution = {}
        for material, weight in zip(materials, weights):
            material_path = getattr(material, 'path', str(material))
            material_name = os.path.basename(material_path)
            usage_count = self.material_stats.get(material_path, MaterialInfo("", "", 0)).usage_count
            weight_distribution[material_name] = {
                'weight': weight,
                'usage_count': usage_count
            }
        
        self.weight_history.append(weight_distribution)
        
        # 调试输出
        if self.debug_mode:
            self._log_selection_details(materials, weights, selected)
    
    def _log_selection_details(self, materials: List[Any], weights: List[float], selected: List[Any]) -> None:
        """记录选择详情到日志"""
        weight_info = []
        for material, weight in zip(materials, weights):
            material_path = getattr(material, 'path', str(material))
            material_name = os.path.basename(material_path)
            usage_count = self.material_stats.get(material_path, MaterialInfo("", "", 0)).usage_count
            weight_info.append(f"{material_name}(用{usage_count}次,权重{weight:.1f})")
        
        selected_info = []
        for material in selected:
            material_path = getattr(material, 'path', str(material))
            material_name = os.path.basename(material_path)
            selected_info.append(material_name)
        
        self.logger.debug(f"素材选择详情:")
        self.logger.debug(f"  可选素材: {', '.join(weight_info[:5])}{'...' if len(weight_info) > 5 else ''}")
        self.logger.debug(f"  选中素材: {', '.join(selected_info)}")
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计信息"""
        if not self.material_stats:
            return {}
        
        usage_counts = [info.usage_count for info in self.material_stats.values()]
        
        stats = {
            'total_materials': len(self.material_stats),
            'total_selections': self.total_selections,
            'max_usage': max(usage_counts) if usage_counts else 0,
            'min_usage': min(usage_counts) if usage_counts else 0,
            'avg_usage': sum(usage_counts) / len(usage_counts) if usage_counts else 0,
            'usage_std': self._calculate_std(usage_counts),
            'unused_materials': len([c for c in usage_counts if c == 0]),
            'overused_materials': len([c for c in usage_counts if c > (sum(usage_counts) / len(usage_counts) * 1.5)])
        }
        
        return stats
    
    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差"""
        if not values:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def get_usage_distribution_text(self) -> str:
        """获取使用分布的文本描述"""
        stats = self.get_usage_statistics()
        
        if not stats:
            return "暂无统计数据"
        
        return (f"素材统计: 总计{stats['total_materials']}个, "
                f"最多用{stats['max_usage']}次, 最少用{stats['min_usage']}次, "
                f"平均{stats['avg_usage']:.1f}次, 未使用{stats['unused_materials']}个")
    
    def reset_statistics(self) -> None:
        """重置所有统计信息"""
        for info in self.material_stats.values():
            info.usage_count = 0
            info.weight = 1.0
            info.last_used_time = 0.0
        
        self.selection_history.clear()
        self.weight_history.clear()
        self.total_selections = 0
        self.max_usage_count = 0
        
        self.logger.info("📊 素材使用统计已重置")
    
    def get_material_usage_details(self) -> List[Dict[str, Any]]:
        """获取详细的素材使用情况"""
        details = []
        
        for path, info in self.material_stats.items():
            details.append({
                'name': info.name,
                'path': info.path,
                'usage_count': info.usage_count,
                'weight': info.weight,
                'last_used_time': info.last_used_time
            })
        
        # 按使用次数排序
        details.sort(key=lambda x: x['usage_count'], reverse=True)
        
        return details
