#!/usr/bin/env python3
"""
测试片段选择逻辑
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.smart_selector import SmartSelector

def test_segment_selection():
    """测试片段选择逻辑"""
    print("测试片段选择逻辑...")
    
    try:
        selector = SmartSelector()
        
        # 模拟片段数据
        all_segments = [
            # A视频的片段
            {'path': 'A-1.mp4', 'start_time': 0.0, 'duration': 3.0, 'index': 1, 'source': 'A'},
            {'path': 'A-2.mp4', 'start_time': 3.0, 'duration': 2.5, 'index': 2, 'source': 'A'},
            {'path': 'A-3.mp4', 'start_time': 5.5, 'duration': 4.0, 'index': 3, 'source': 'A'},
            {'path': 'A-4.mp4', 'start_time': 9.5, 'duration': 3.5, 'index': 4, 'source': 'A'},
            {'path': 'A-5.mp4', 'start_time': 13.0, 'duration': 2.0, 'index': 5, 'source': 'A'},
            {'path': 'A-6.mp4', 'start_time': 15.0, 'duration': 3.0, 'index': 6, 'source': 'A'},
            
            # B视频的片段
            {'path': 'B-1.mp4', 'start_time': 0.0, 'duration': 2.0, 'index': 1, 'source': 'B'},
            {'path': 'B-2.mp4', 'start_time': 2.0, 'duration': 3.0, 'index': 2, 'source': 'B'},
            {'path': 'B-3.mp4', 'start_time': 5.0, 'duration': 2.5, 'index': 3, 'source': 'B'},
            {'path': 'B-4.mp4', 'start_time': 7.5, 'duration': 4.0, 'index': 4, 'source': 'B'},
            {'path': 'B-5.mp4', 'start_time': 11.5, 'duration': 3.0, 'index': 5, 'source': 'B'},
            
            # C视频的片段
            {'path': 'C-1.mp4', 'start_time': 0.0, 'duration': 3.5, 'index': 1, 'source': 'C'},
            {'path': 'C-2.mp4', 'start_time': 3.5, 'duration': 2.0, 'index': 2, 'source': 'C'},
            {'path': 'C-3.mp4', 'start_time': 5.5, 'duration': 3.0, 'index': 3, 'source': 'C'},
            {'path': 'C-4.mp4', 'start_time': 8.5, 'duration': 2.5, 'index': 4, 'source': 'C'},
            {'path': 'C-5.mp4', 'start_time': 11.0, 'duration': 4.0, 'index': 5, 'source': 'C'},
            {'path': 'C-6.mp4', 'start_time': 15.0, 'duration': 2.0, 'index': 6, 'source': 'C'},
        ]
        
        print(f"总共 {len(all_segments)} 个片段")
        
        # 测试选择7个片段
        target_duration = 20.0  # 目标20秒
        selected = selector.select_segments(all_segments, target_duration)
        
        print(f"\n选择结果:")
        print(f"目标时长: {target_duration}秒")
        print(f"选择片段数: {len(selected)}")
        
        total_duration = 0
        for i, segment in enumerate(selected, 1):
            total_duration += segment['duration']
            display_name = segment.get('display_name', f"{segment['source']}-{segment['index']}")
            original_name = segment.get('original_name', f"{segment['source']}-{segment['index']}")
            print(f"{i}. {display_name} (原名: {original_name}, 时长: {segment['duration']}s)")

        print(f"总时长: {total_duration:.2f}秒")

        # 显示最终的选择序列
        display_sequence = []
        for segment in selected:
            display_name = segment.get('display_name', f"{segment['source']}-{segment['index']}")
            display_sequence.append(display_name)

        print(f"最终序列: {' -> '.join(display_sequence)}")

        # 验证全局序号是否递增
        global_indices = []
        for i, segment in enumerate(selected, 1):
            display_name = segment.get('display_name', f"{segment['source']}-{segment['index']}")
            # 提取序号
            if '-' in display_name:
                try:
                    global_index = int(display_name.split('-')[1])
                    global_indices.append(global_index)
                except:
                    pass

        if global_indices == list(range(1, len(global_indices) + 1)):
            print("✓ 全局序号递增正确")
        else:
            print(f"⚠ 全局序号有问题: {global_indices}")

        # 检查是否有重复使用的片段
        original_names = []
        for segment in selected:
            original_name = segment.get('original_name', f"{segment['source']}-{segment['index']}")
            original_names.append(original_name)

        if len(original_names) == len(set(original_names)):
            print("✓ 没有重复使用片段")
        else:
            print("⚠ 有重复使用的片段")

        # 检查随机性
        sources_in_sequence = [segment['source'] for segment in selected]
        unique_patterns = len(set(zip(sources_in_sequence[:-1], sources_in_sequence[1:])))
        if unique_patterns > 1:
            print("✓ 具有随机性")
        else:
            print("⚠ 可能缺乏随机性")
        
        print("✓ 片段选择测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_segment_selection()
    sys.exit(0 if success else 1)
