#!/usr/bin/env python3
"""
测试片段选择逻辑
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.smart_selector import SmartSelector

def test_segment_selection():
    """测试片段选择逻辑"""
    print("测试片段选择逻辑...")
    
    try:
        selector = SmartSelector()
        
        # 模拟片段数据
        all_segments = [
            # A视频的片段
            {'path': 'A-1.mp4', 'start_time': 0.0, 'duration': 3.0, 'index': 1, 'source': 'A'},
            {'path': 'A-2.mp4', 'start_time': 3.0, 'duration': 2.5, 'index': 2, 'source': 'A'},
            {'path': 'A-3.mp4', 'start_time': 5.5, 'duration': 4.0, 'index': 3, 'source': 'A'},
            {'path': 'A-4.mp4', 'start_time': 9.5, 'duration': 3.5, 'index': 4, 'source': 'A'},
            {'path': 'A-5.mp4', 'start_time': 13.0, 'duration': 2.0, 'index': 5, 'source': 'A'},
            {'path': 'A-6.mp4', 'start_time': 15.0, 'duration': 3.0, 'index': 6, 'source': 'A'},
            
            # B视频的片段
            {'path': 'B-1.mp4', 'start_time': 0.0, 'duration': 2.0, 'index': 1, 'source': 'B'},
            {'path': 'B-2.mp4', 'start_time': 2.0, 'duration': 3.0, 'index': 2, 'source': 'B'},
            {'path': 'B-3.mp4', 'start_time': 5.0, 'duration': 2.5, 'index': 3, 'source': 'B'},
            {'path': 'B-4.mp4', 'start_time': 7.5, 'duration': 4.0, 'index': 4, 'source': 'B'},
            {'path': 'B-5.mp4', 'start_time': 11.5, 'duration': 3.0, 'index': 5, 'source': 'B'},
            
            # C视频的片段
            {'path': 'C-1.mp4', 'start_time': 0.0, 'duration': 3.5, 'index': 1, 'source': 'C'},
            {'path': 'C-2.mp4', 'start_time': 3.5, 'duration': 2.0, 'index': 2, 'source': 'C'},
            {'path': 'C-3.mp4', 'start_time': 5.5, 'duration': 3.0, 'index': 3, 'source': 'C'},
            {'path': 'C-4.mp4', 'start_time': 8.5, 'duration': 2.5, 'index': 4, 'source': 'C'},
            {'path': 'C-5.mp4', 'start_time': 11.0, 'duration': 4.0, 'index': 5, 'source': 'C'},
            {'path': 'C-6.mp4', 'start_time': 15.0, 'duration': 2.0, 'index': 6, 'source': 'C'},
        ]
        
        print(f"总共 {len(all_segments)} 个片段")
        
        # 测试选择7个片段
        target_duration = 20.0  # 目标20秒
        selected = selector.select_segments(all_segments, target_duration)
        
        print(f"\n选择结果:")
        print(f"目标时长: {target_duration}秒")
        print(f"选择片段数: {len(selected)}")
        
        total_duration = 0
        for i, segment in enumerate(selected, 1):
            total_duration += segment['duration']
            print(f"{i}. {segment['source']}-{segment['index']} "
                  f"(时长: {segment['duration']}s)")
        
        print(f"总时长: {total_duration:.2f}秒")
        
        # 验证轮换逻辑
        sources_order = [s['source'] for s in selected]
        print(f"来源顺序: {' -> '.join(sources_order)}")
        
        # 检查是否按预期轮换
        expected_pattern = ['A', 'B', 'C']
        is_rotating = True
        for i in range(min(len(selected), 6)):  # 检查前6个
            expected_source = expected_pattern[i % 3]
            if selected[i]['source'] != expected_source:
                is_rotating = False
                break
        
        if is_rotating:
            print("✓ 轮换逻辑正确")
        else:
            print("⚠ 轮换逻辑可能有问题")
        
        print("✓ 片段选择测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_segment_selection()
    sys.exit(0 if success else 1)
