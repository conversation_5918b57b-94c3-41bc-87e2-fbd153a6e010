"""
设备ID生成模块
将原JavaScript的设备ID生成逻辑转换为Python
"""
import platform
import subprocess
import sys
import hashlib
import re


class DeviceIDGenerator:
    """设备ID生成器"""
    @classmethod
    def generate_device_id(cls):
        return get_machine_code()

def get_machine_code():
    # 获取更稳定的系统标识
    identifiers = {
        'bios_serial': _get_windows_bios_serial() if platform.system() == 'Windows' 
                     else _get_linux_machine_id(),
        'cpu_serial': _get_cpu_serial(),
        'baseboard_serial': _get_baseboard_serial()
    }
    
    # 生成哈希
    hash_str = ''.join(v for v in identifiers.values() if v).encode('utf-8')
    code = hashlib.sha256(hash_str).hexdigest().upper()
    return re.sub(r'[^A-Z0-9]', '', code)[:32]

def _get_windows_bios_serial():
    try:
        kwargs = {'shell': True}
        if sys.platform == 'win32':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        return subprocess.check_output(
            'wmic bios get serialnumber',
            **kwargs
        ).decode().split('\n')[1].strip()
    except:
        return ""

def _get_linux_machine_id():
    try:
        with open('/etc/machine-id') as f:
            return f.read().strip()
    except:
        return ""

def _get_cpu_serial():
    if platform.system() == 'Windows':
        cmd = 'wmic cpu get processorid'
    else:
        cmd = 'cat /proc/cpuinfo | grep serial | head -n 1'
    try:
        kwargs = {'shell': True}
        if sys.platform == 'win32':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        return subprocess.check_output(cmd, **kwargs).decode().split()[-1]
    except:
        return ""

def _get_baseboard_serial():
    if platform.system() == 'Windows':
        cmd = 'wmic baseboard get serialnumber'
    else:
        cmd = 'dmidecode -s baseboard-serial-number'
    try:
        kwargs = {'shell': True}
        if sys.platform == 'win32':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        return subprocess.check_output(cmd, **kwargs).decode().split()[-1]
    except:
        return ""

# 全局实例
device_id_generator = DeviceIDGenerator()


def get_device_id() -> str:
    """获取设备ID的便捷函数"""
    return device_id_generator.generate_device_id()

# 测试代码
if __name__ == "__main__":
    generator = DeviceIDGenerator()
    
    print(f"设备ID: {generator.generate_device_id()}")