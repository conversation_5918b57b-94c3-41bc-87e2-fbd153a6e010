#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权管理模块 - 易游网络验证
Authorization Management Module - EyData Network Verification
"""

import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlencode
from PyQt5.QtCore import QObject, pyqtSignal


class AuthManager(QObject):
    """授权管理器 - 易游网络验证"""

    # 信号定义
    login_result = pyqtSignal(bool, str, int)  # 登录结果(成功/失败, 消息, 过期时间戳)

    def __init__(self):
        super().__init__()
        # 验证配置
        self.single_code = ""  # 用户输入的卡密
        self.version = "1.0"  # 版本号
        self.device_id = ""
        self.status_code = ""  # 登录成功后的状态码
        self.expire_timestamp = 0
        self.is_logged_in = False

        # API接口地址
        self.login_url = "http://w3.eydata.net/F22DAD5C780E9562"
        self.get_expired_url = "https://vip1.eydata.net/A81DC01C6A563BD1"
        self.check_status_url = "https://vip1.eydata.net/053F0E6ED2CAD8B6"
        self.logout_url = "https://vip1.eydata.net/10B92A1FE20CE83D"
    
    def set_device_info(self, device_id: str):
        """设置设备信息"""
        self.device_id = device_id

    def set_card_key(self, card_key: str):
        """设置卡密"""
        self.single_code = card_key

    def http_post(self, url: str, data: Dict[str, Any]) -> str:
        """发送POST请求到易游网络验证"""
        try:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            url_encoded = urlencode([(key, value) for key, value in data.items()])
            response = requests.post(url, data=url_encoded, headers=headers, timeout=30)

            if response.status_code == 200:
                return str(response.text)
            else:
                return "遇到错误，请检查网络重试"
        except Exception as e:
            print(f"网络请求异常: {e}")
            return "遇到错误，请检查网络重试"

    def login(self) -> Tuple[bool, str, int]:
        """单码登录"""
        try:
            if not self.device_id:
                return False, "设备ID为空", 0

            if not self.single_code:
                return False, "请先输入卡密", 0

            # 构建登录请求数据
            data = {
                'SingleCode': self.single_code,  # 单码卡密
                'Ver': self.version,  # 版本号
                'Mac': self.device_id,  # 机器码（使用设备ID）
            }

            # 发送登录请求
            result = self.http_post(self.login_url, data)

            if len(result) == 32:
                # 登录成功，状态码为32位字符串
                self.status_code = result
                self.is_logged_in = True

                # 获取到期时间
                print(f"登录成功，状态码: {self.status_code}")
                expire_timestamp = self.get_expire_time()
                if expire_timestamp > 0:
                    self.expire_timestamp = expire_timestamp
                    # 立即检查是否过期
                    if self.is_expired():
                        self.is_logged_in = False
                        self.status_code = ""
                        return False, "卡密已过期，请联系管理员", 0
                    return True, "登录成功", expire_timestamp
                else:
                    print("无法获取到期时间")
                    return True, "登录成功，但无法获取到期时间", 0
            else:
                # 登录失败
                return False, f"登录失败：{result}", 0

        except Exception as e:
            print(f"登录异常: {e}")
            return False, "登录过程中发生错误", 0

    def get_expire_time(self) -> int:
        """获取用户到期时间"""
        try:
            if not self.single_code:
                return 0
            data = {
                'UserName': self.single_code  # 使用卡密作为用户名
            }

            result = self.http_post(self.get_expired_url, data)
            # 尝试解析返回的时间戳或时间字符串
            try:
                # 如果返回的是时间戳（秒）
                if result.isdigit():
                    timestamp = int(result) * 1000  # 转换为毫秒
                    return timestamp
                # 如果返回的是日期字符串，尝试解析
                # 格式: 2076-05-23 10:32:10
                if '-' in result and ':' in result:
                    from datetime import datetime
                    dt = datetime.strptime(result.strip(), '%Y-%m-%d %H:%M:%S')
                    timestamp = int(dt.timestamp())  # 转换为秒时间戳，不乘以1000
                    return timestamp

                print(f"无法识别的时间格式: '{result}'")
                return 0
            except Exception as parse_error:
                print(f"解析时间失败: {parse_error}, 原始数据: '{result}'")
                return 0

        except Exception as e:
            print(f"获取到期时间异常: {e}")
            return 0

    def check_login(self, is_manual_check: bool = False) -> Tuple[bool, str, int]:
        """检查登录状态"""
        print(f"check_login 被调用 - is_logged_in: {self.is_logged_in}, status_code: {bool(self.status_code)}")

        if not self.is_logged_in or not self.status_code:
            # 如果未登录，尝试自动登录
            return self.login()
        # 如果已登录，先检查是否过期
        if self.is_expired():
            self.is_logged_in = False
            self.status_code = ""
            return False, "使用时间已到期", 0

        # 检查用户状态
        try:
            data = {
                'StatusCode': self.status_code,
                'UserName': self.single_code
            }

            result = self.http_post(self.check_status_url, data)

            if "成功" in result or len(result) == 32:
                # 状态正常，检查是否过期
                if self.is_expired():
                    return False, "使用时间已到期", 0
                return True, "授权验证成功", self.expire_timestamp
            else:
                # 状态异常，需要重新登录
                self.is_logged_in = False
                self.status_code = ""
                return self.login()

        except Exception as e:
            print(f"检查登录状态异常: {e}")
            return False, "网络开小差儿了", 0
    
    def check_login_async(self, is_manual_check: bool = False):
        """异步检查登录状态"""
        try:
            success, message, expire_ts = self.check_login(is_manual_check)
            self.login_result.emit(success, message, expire_ts)
        except Exception as e:
            print(f"异步检查登录异常: {e}")
            self.login_result.emit(False, "检查登录状态失败", 0)
    
    def get_expire_time_string(self) -> str:
        """获取过期时间字符串"""
        print(f"get_expire_time_string 被调用，expire_timestamp = {self.expire_timestamp}")
        if self.expire_timestamp <= 0:
            print("expire_timestamp <= 0，返回'未知'")
            return "未知"

        try:
            expire_datetime = datetime.fromtimestamp(self.expire_timestamp)  # 直接使用秒时间戳
            result = expire_datetime.strftime("%Y-%m-%d %H:%M:%S")
            print(f"格式化后的时间字符串: {result}")
            return result
        except Exception as e:
            print(f"格式化时间失败: {e}")
            return "格式错误"
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        if self.expire_timestamp <= 0:
            return True

        current_timestamp = int(datetime.now().timestamp())  # 使用秒时间戳

        # 调试信息
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        expire_time_str = datetime.fromtimestamp(self.expire_timestamp).strftime("%Y-%m-%d %H:%M:%S")
        print(f"过期检查 - 当前时间: {current_time_str} ({current_timestamp})")
        print(f"过期检查 - 到期时间: {expire_time_str} ({self.expire_timestamp})")
        print(f"过期检查 - 是否过期: {self.expire_timestamp <= current_timestamp}")

        return self.expire_timestamp <= current_timestamp
    
    def get_remaining_days(self) -> int:
        """获取剩余天数"""
        if self.expire_timestamp <= 0:
            return 0
        
        current_timestamp = int(datetime.now().timestamp() * 1000)
        if self.expire_timestamp <= current_timestamp:
            return 0
        
        remaining_ms = self.expire_timestamp - current_timestamp
        remaining_days = remaining_ms // (24 * 60 * 60 * 1000)
        
        return int(remaining_days)
    
    def logout(self):
        """登出"""
        try:
            if self.status_code:
                # 调用退出登录接口
                data = {
                    'StatusCode': self.status_code,
                    'UserName': self.status_code
                }
                result = self.http_post(self.logout_url, data)
                print(f"退出登录结果: {result}")
        except Exception as e:
            print(f"退出登录异常: {e}")
        finally:
            # 清除本地状态
            self.is_logged_in = False
            self.expire_timestamp = 0
            self.status_code = ""
    
    def get_auth_info(self) -> str:
        """获取授权信息"""
        return f"设备ID: {self.device_id}\n请输入您的卡密进行验证"
    



# 测试代码
if __name__ == "__main__":
    from ..utils.device_id import DeviceIDGenerator

    # 生成设备ID
    device_id = DeviceIDGenerator.generate_device_id()

    # 创建授权管理器
    auth_manager = AuthManager()
    auth_manager.set_device_info(device_id)

    # 测试登录检查
    success, message, expire_ts = auth_manager.check_login(True)

    print(f"登录检查结果: {success}")
    print(f"消息: {message}")
    if success:
        print(f"过期时间: {auth_manager.get_expire_time_string()}")
        print(f"剩余天数: {auth_manager.get_remaining_days()}")

    # 获取授权信息
    auth_info = auth_manager.get_auth_url()
    print(f"授权信息: {auth_info}")
