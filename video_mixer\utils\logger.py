#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器
Logger Manager
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional

class VideoMixerLogger:
    """视频混剪工具专用日志记录器"""
    
    def __init__(self, log_dir: Optional[str] = None):
        self.log_dir = log_dir or os.path.expanduser("~/.video_mixer/logs")
        self.ensure_log_dir()
        
        # 创建日志文件名（按日期）
        today = datetime.now().strftime("%Y%m%d")
        self.log_file = os.path.join(self.log_dir, f"video_mixer_{today}.log")
        
        # 设置日志记录器
        self.logger = logging.getLogger('VideoMixer')
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self.setup_handlers()
    
    def ensure_log_dir(self):
        """确保日志目录存在"""
        try:
            os.makedirs(self.log_dir, exist_ok=True)
        except Exception as e:
            print(f"创建日志目录失败: {e}")
    
    def setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器
        try:
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            
        except Exception as e:
            print(f"设置日志处理器失败: {e}")
    
    def debug(self, message: str):
        """调试信息"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """一般信息"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """警告信息"""
        self.logger.warning(message)
    
    def error(self, message: str, exception: Optional[Exception] = None):
        """错误信息"""
        self.logger.error(message)
        if exception:
            self.logger.error(f"异常详情: {str(exception)}")
            self.logger.error(f"异常类型: {type(exception).__name__}")
            # 记录完整的traceback
            self.logger.error("异常堆栈:")
            for line in traceback.format_exc().split('\n'):
                if line.strip():
                    self.logger.error(f"  {line}")
    
    def log_ffmpeg_command(self, command: list, description: str = ""):
        """记录FFmpeg命令（仅记录操作类型，不记录具体命令）"""
        self.info(f"🎬 开始执行 {description}")

    def log_ffmpeg_result(self, returncode: int, stdout: str, stderr: str, description: str = ""):
        """记录FFmpeg执行结果（仅记录成功/失败状态和关键错误信息）"""
        if returncode == 0:
            self.info(f"✅ 执行成功 {description}")
        else:
            self.error(f"❌ 执行失败 {description} (返回码: {returncode})")

            # 只记录关键错误信息，过滤掉可能包含路径的详细信息
            if stderr:
                # 提取关键错误行，过滤掉包含路径的行
                error_lines = stderr.split('\n')
                key_errors = []
                for line in error_lines:
                    line = line.strip()
                    # 只记录真正的错误信息，跳过包含路径、版本信息等的行
                    if any(keyword in line.lower() for keyword in ['error', 'failed', 'invalid', 'cannot']):
                        # 移除可能的路径信息
                        if not any(char in line for char in ['\\', '/', ':']):
                            key_errors.append(line)

                if key_errors:
                    self.error(f"关键错误: {'; '.join(key_errors[:3])}")  # 最多记录3条关键错误
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True, error_msg: str = ""):
        """记录文件操作（不记录具体路径）"""
        if success:
            self.info(f"📁 文件操作成功: {operation}")
        else:
            self.error(f"📁 文件操作失败: {operation} - {error_msg}")

    def log_video_info(self, video_path: str, info: dict):
        """记录视频信息（不记录具体路径）"""
        self.info(f"🎥 获取视频信息成功")
        # 只记录基本的技术参数，不记录路径
        if info:
            self.debug(f"  时长: {info.get('duration', 'N/A')}秒")
            self.debug(f"  分辨率: {info.get('width', 'N/A')}x{info.get('height', 'N/A')}")
            self.debug(f"  帧率: {info.get('fps', 'N/A')}")
    
    def log_generation_start(self, total_videos: int, algorithm: str):
        """记录生成开始"""
        self.info(f"🚀 开始生成视频: {total_videos}个, 算法: {algorithm}")
    
    def log_generation_progress(self, current: int, total: int, video_path: str = ""):
        """记录生成进度"""
        progress = (current / total) * 100 if total > 0 else 0
        self.info(f"📊 生成进度: {current}/{total} ({progress:.1f}%) {video_path}")
    
    def log_generation_complete(self, successful: int, total: int, output_dir: str):
        """记录生成完成（不记录具体路径）"""
        self.info(f"🎉 生成完成: 成功{successful}/{total}个视频")
    
    def log_database_operation(self, operation: str, success: bool, details: str = ""):
        """记录数据库操作"""
        if success:
            self.info(f"🗄️ 数据库操作成功: {operation} {details}")
        else:
            self.warning(f"🗄️ 数据库操作失败: {operation} {details}")
    
    def log_system_info(self):
        """记录系统信息（简化版本，不记录路径）"""
        import platform
        self.info("💻 系统信息:")
        self.info(f"  操作系统: {platform.system()} {platform.release()}")
        self.info(f"  Python版本: {sys.version.split()[0]}")  # 只记录版本号
        self.info("  日志系统已启动")
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return self.log_file
    
    def get_recent_logs(self, lines: int = 50) -> str:
        """获取最近的日志内容"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                return ''.join(recent_lines)
        except Exception as e:
            return f"读取日志失败: {e}"

# 全局日志实例
_global_logger = None

def get_logger() -> VideoMixerLogger:
    """获取全局日志实例"""
    global _global_logger
    if _global_logger is None:
        _global_logger = VideoMixerLogger()
    return _global_logger

def init_logger(log_dir: Optional[str] = None) -> VideoMixerLogger:
    """初始化日志系统"""
    global _global_logger
    _global_logger = VideoMixerLogger(log_dir)
    _global_logger.log_system_info()
    return _global_logger
