#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理核心模块
Video Processing Core Module
"""

import os
import subprocess
import json
import tempfile
import shutil
import platform
import sys
from typing import List, Dict, Any, Tuple, Optional, Callable
from dataclasses import dataclass

try:
    from ..utils.logger import get_logger
except ImportError:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger
from pathlib import Path


@dataclass
class VideoInfo:
    """视频信息数据类"""
    path: str
    duration: float  # 秒
    width: int
    height: int
    fps: float
    size: int  # 文件大小（字节）


@dataclass
class ProcessingParams:
    """视频处理参数"""
    speed_scale: float = 1.0
    size_scale: float = 1.0
    start_time: float = 0.0
    duration: float = 0.0
    output_width: int = 1080
    output_height: int = 1440
    crop_x: int = 0
    crop_y: int = 0


class VideoProcessor:
    """视频处理器"""

    def __init__(self, ffmpeg_path: str = "ffmpeg", ffprobe_path: str = "ffprobe"):
        self.logger = get_logger()
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path

        self.logger.info(f"🎬 视频处理器初始化完成")
        self.temp_dir = None

        # 验证FFmpeg是否可用
        if not self._check_ffmpeg():
            raise RuntimeError("FFmpeg不可用，请确保已安装并添加到环境变量")

    def _get_subprocess_kwargs(self) -> dict:
        """获取适合的subprocess参数，在Windows下隐藏控制台窗口"""
        kwargs = {
            'capture_output': True,
            'text': True,
        }

        # 在Windows下隐藏控制台窗口
        if platform.system() == 'Windows':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

        return kwargs

    def _get_popen_kwargs(self) -> dict:
        """获取适合的Popen参数，在Windows下隐藏控制台窗口"""
        kwargs = {
            'stdout': subprocess.PIPE,
            'stderr': subprocess.PIPE,
            'text': True,
            'universal_newlines': True,
            'encoding': 'utf-8',
            'errors': 'ignore'
        }

        # 在Windows下隐藏控制台窗口
        if platform.system() == 'Windows':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

        return kwargs
    
    def _check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            kwargs = self._get_subprocess_kwargs()
            kwargs['timeout'] = 10
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                **kwargs
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def get_video_info(self, video_path: str) -> Optional[VideoInfo]:
        """获取视频信息"""
        try:
            if not os.path.exists(video_path):
                self.logger.error(f"视频文件不存在: {video_path}")
                return None

            # 使用ffprobe获取视频信息
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                video_path
            ]

            self.logger.log_ffmpeg_command(cmd, "获取视频信息")

            # 尝试多种编码方式
            for encoding in ['utf-8', 'gbk', 'cp1252']:
                try:
                    kwargs = self._get_subprocess_kwargs()
                    kwargs.update({
                        'timeout': 30,
                        'encoding': encoding,
                        'errors': 'ignore'
                    })
                    result = subprocess.run(cmd, **kwargs)

                    self.logger.log_ffmpeg_result(
                        result.returncode,
                        result.stdout or "",
                        result.stderr or "",
                        f"获取视频信息 (编码: {encoding})"
                    )

                    if result.returncode != 0:
                        continue

                    if not result.stdout or result.stdout.strip() == "":
                        continue

                    data = json.loads(result.stdout)
                    break

                except (subprocess.TimeoutExpired, json.JSONDecodeError, UnicodeDecodeError) as e:
                    self.logger.warning(f"编码 {encoding} 解析失败: {str(e)}")
                    continue
            else:
                self.logger.error(f"获取视频信息失败，所有编码尝试都失败: {video_path}")
                return None
            
            # 查找视频流
            video_stream = None
            for stream in data.get("streams", []):
                if stream.get("codec_type") == "video":
                    video_stream = stream
                    break
            
            if not video_stream:
                print(f"未找到视频流: {video_path}")
                return None
            
            # 提取信息
            duration = float(data.get("format", {}).get("duration", 0))
            width = int(video_stream.get("width", 0))
            height = int(video_stream.get("height", 0))
            
            # 计算FPS
            fps_str = video_stream.get("r_frame_rate", "30/1")
            if "/" in fps_str:
                num, den = fps_str.split("/")
                fps = float(num) / float(den) if float(den) != 0 else 30.0
            else:
                fps = float(fps_str)
            
            # 文件大小
            size = int(data.get("format", {}).get("size", 0))
            
            return VideoInfo(
                path=video_path,
                duration=duration,
                width=width,
                height=height,
                fps=fps,
                size=size
            )
            
        except Exception as e:
            print(f"获取视频信息异常: {e}")
            # 尝试使用简化方法获取基本信息
            return self._get_video_info_simple(video_path)

    def _get_video_info_simple(self, video_path: str) -> Optional[VideoInfo]:
        """简化的视频信息获取方法"""
        try:
            # 获取文件大小
            size = os.path.getsize(video_path)

            # 使用默认值创建VideoInfo
            return VideoInfo(
                path=video_path,
                duration=60.0,  # 默认60秒
                width=1920,     # 默认1920x1080
                height=1080,
                fps=30.0,       # 默认30fps
                size=size
            )

        except Exception as e:
            print(f"简化视频信息获取也失败: {e}")
            return None

    def get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            if not os.path.exists(audio_path):
                print(f"音频文件不存在: {audio_path}")
                return 0.0

            # 使用ffprobe获取音频时长
            cmd = [
                self.ffprobe_path,
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                audio_path
            ]

            # 尝试多种编码方式
            for encoding in ['utf-8', 'gbk', 'cp1252']:
                try:
                    kwargs = self._get_subprocess_kwargs()
                    kwargs.update({
                        'timeout': 30,
                        'encoding': encoding,
                        'errors': 'ignore'
                    })
                    result = subprocess.run(cmd, **kwargs)

                    if result.returncode != 0:
                        continue

                    if not result.stdout or result.stdout.strip() == "":
                        continue

                    data = json.loads(result.stdout)

                    # 提取时长信息
                    format_info = data.get("format", {})
                    duration_str = format_info.get("duration")

                    if duration_str:
                        duration = float(duration_str)
                        print(f"音频时长: {duration:.2f}秒")
                        return duration

                except (subprocess.TimeoutExpired, json.JSONDecodeError, UnicodeDecodeError, ValueError):
                    continue

            print(f"获取音频时长失败，所有编码尝试都失败: {audio_path}")
            return 0.0

        except Exception as e:
            print(f"获取音频时长异常: {e}")
            return 0.0
    
    def create_temp_dir(self) -> str:
        """创建临时目录"""
        if self.temp_dir is None:
            self.temp_dir = tempfile.mkdtemp(prefix="video_mixer_")
        return self.temp_dir
    
    def cleanup_temp_dir(self):
        """清理临时目录"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None
            except Exception as e:
                print(f"清理临时目录失败: {e}")
    
    def process_video_segment(
        self,
        input_path: str,
        output_path: str,
        params: ProcessingParams,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> bool:
        """处理单个视频片段"""
        try:
            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-vf", self._build_video_filter(params),
                "-c:v", "libx264",
                "-crf", "12",
                "-preset", "medium",
                "-maxrate", "5000k",
                "-bufsize", "10000k",
                "-x264-params", "keyint=30:min-keyint=30",
                "-movflags", "+faststart",
                "-profile:v", "high",
                "-pix_fmt", "yuv420p",
                "-an",  # 移除音频
                # 移除 -t 参数，时长控制完全由滤镜处理
                "-y",  # 覆盖输出文件
                output_path
            ]
            
            self.logger.log_ffmpeg_command(cmd, "处理视频片段")

            # 执行命令
            kwargs = self._get_popen_kwargs()
            process = subprocess.Popen(cmd, **kwargs)

            # 收集所有输出用于日志记录
            stderr_output = []

            # 监控进度
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    stderr_output.append(output.strip())
                    if progress_callback:
                        # 解析进度信息
                        progress = self._parse_progress(output, params.duration)
                        if progress >= 0:
                            progress_callback(progress)

            # 等待完成
            return_code = process.wait()

            # 记录完整的FFmpeg输出
            full_stderr = '\n'.join(stderr_output)
            self.logger.log_ffmpeg_result(
                return_code,
                "",
                full_stderr,
                "处理视频片段"
            )

            if return_code == 0:
                self.logger.info(f"✅ 视频片段处理完成")
                return True
            else:
                self.logger.error(f"❌ 视频片段处理失败")
                return False
                
        except Exception as e:
            print(f"处理视频片段异常: {e}")
            return False
    
    def _build_video_filter(self, params: ProcessingParams) -> str:
        """构建视频滤镜字符串"""
        filters = []

        # 1. 首先进行时间裁剪（在原始时间轴上）
        if params.start_time > 0 or params.duration > 0:
            trim_filter = f"trim=start={params.start_time}"
            if params.duration > 0:
                # 计算结束时间，考虑速度调整
                # 如果要加速播放，需要更长的原始素材
                original_duration_needed = params.duration / params.speed_scale
                trim_filter += f":duration={original_duration_needed}"
            filters.append(trim_filter)
            filters.append("setpts=PTS-STARTPTS")

        # 2. 调整播放速度（在裁剪后的时间轴上）
        if params.speed_scale != 1.0:
            filters.append(f"setpts=PTS/{params.speed_scale}")

        # 3. 缩放
        if params.size_scale != 1.0:
            filters.append(f"scale=iw*{params.size_scale}:ih*{params.size_scale}")

        # 4. 最后裁剪到目标尺寸
        filters.append(f"crop={params.output_width}:{params.output_height}:(iw-{params.output_width})/2:(ih-{params.output_height})/2")

        return ",".join(filters)
    
    def _parse_progress(self, output: str, total_duration: float) -> float:
        """解析FFmpeg输出中的进度信息"""
        try:
            if "time=" in output:
                # 提取时间信息
                time_part = output.split("time=")[1].split()[0]
                
                # 解析时间格式 HH:MM:SS.mmm
                time_parts = time_part.split(":")
                if len(time_parts) == 3:
                    hours = float(time_parts[0])
                    minutes = float(time_parts[1])
                    seconds = float(time_parts[2])
                    
                    current_time = hours * 3600 + minutes * 60 + seconds
                    
                    if total_duration > 0:
                        progress = min(current_time / total_duration * 100, 100)
                        return progress
            
            return -1
            
        except Exception:
            return -1
    
    def merge_video_segments(
        self,
        segment_paths: List[str],
        audio_path: str,
        subtitle_path: str,
        output_path: str,
        subtitle_style: Dict[str, Any],
        total_duration: float,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> bool:
        """合并视频片段并添加音频和字幕"""
        try:
            # 构建输入参数
            inputs = []
            for segment_path in segment_paths:
                inputs.extend(["-i", segment_path])

            inputs.extend(["-i", audio_path])
            inputs.extend(["-i", subtitle_path])

            # 构建复杂的滤镜图
            filter_complex = self._build_merge_filter(len(segment_paths), subtitle_path, subtitle_style)

            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path
            ] + inputs + [
                "-filter_complex", filter_complex,
                "-map", "[sub]",
                "-map", f"{len(segment_paths)}:a",
                "-c:v", "libx264",
                "-crf", "12",
                "-preset", "medium",
                "-maxrate", "5000k",
                "-bufsize", "10000k",
                "-x264-params", "keyint=30:min-keyint=30:scenecut=40",
                "-movflags", "+faststart",
                "-profile:v", "high",
                "-pix_fmt", "yuv420p",
                "-c:a", "aac",
                "-b:a", "192k",
                "-t", str(total_duration),
                "-y",
                output_path
            ]

            self.logger.log_ffmpeg_command(cmd, f"合并视频: {len(segment_paths)}个片段")

            # 执行命令
            kwargs = self._get_popen_kwargs()
            process = subprocess.Popen(cmd, **kwargs)

            # 收集所有输出用于日志记录
            stderr_output = []

            # 监控进度
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    stderr_output.append(output.strip())
                    if progress_callback:
                        progress = self._parse_progress(output, total_duration)
                        if progress >= 0:
                            progress_callback(progress)

            return_code = process.wait()

            # 记录完整的FFmpeg输出
            full_stderr = '\n'.join(stderr_output)
            self.logger.log_ffmpeg_result(
                return_code,
                "",
                full_stderr,
                f"合并视频: {len(segment_paths)}个片段"
            )

            if return_code == 0:
                self.logger.info(f"✅ 视频合并完成")
                return True
            else:
                self.logger.error(f"❌ 视频合并失败")
                print(f"视频合并失败: {stderr}")
                return False

        except Exception as e:
            print(f"合并视频异常: {e}")
            return False
    
    def _build_merge_filter(self, segment_count: int, subtitle_path: str, subtitle_style: Dict[str, Any]) -> str:
        """构建合并滤镜"""
        self.logger.debug(f"构建合并滤镜: {segment_count}个片段")

        # 首先统一所有输入的SAR值，避免concat失败
        # 为每个输入添加setsar滤镜，统一设置为1:1
        sar_filters = []
        for i in range(segment_count):
            sar_filters.append(f"[{i}:v]setsar=1:1[v{i}]")

        # 构建concat输入引用
        concat_inputs = "".join([f"[v{i}]" for i in range(segment_count)])
        concat_filter = f"{concat_inputs}concat=n={segment_count}:v=1:a=0[vid]"

        # 设置帧率
        fps_filter = "[vid]fps=30[fpsvid]"

        # 添加字幕
        subtitle_filter = self._build_subtitle_filter(subtitle_path, subtitle_style)

        # 组合所有滤镜
        all_filters = ";".join(sar_filters) + ";" + concat_filter + ";" + fps_filter + ";[fpsvid]" + subtitle_filter + "[sub]"

        self.logger.debug(f"生成的滤镜链: {all_filters}")
        return all_filters
    
    def _build_subtitle_filter(self, subtitle_path: str, style: Dict[str, Any]) -> str:
        """构建字幕滤镜"""
        # 转换路径格式（Windows路径需要特殊处理）
        safe_path = subtitle_path.replace('\\', '/').replace(':', '\\:')
        
        # 构建样式字符串
        style_parts = [
            f"FontName={style.get('font_family', 'Arial')}",
            f"FontSize={style.get('font_size', 11)}",
            f"FontWeight=900",
            f"PrimaryColour=&H{self._html_color_to_ass(style.get('color', '#ffffff'))}",
            f"MarginV={style.get('bottom_margin', 50)}",
            f"Outline={style.get('outline_size', 1)}",
            f"OutlineColour=&H{self._html_color_to_ass(style.get('outline_color', '#000000'))}"
        ]
        
        style_string = ",".join(style_parts)
        
        return f"subtitles='{safe_path}':force_style='{style_string}'"
    
    def _html_color_to_ass(self, html_color: str) -> str:
        """将HTML颜色转换为ASS颜色格式"""
        try:
            # 移除#号
            color = html_color.replace('#', '')
            
            # 如果是3位简写，扩展为6位
            if len(color) == 3:
                color = ''.join([c*2 for c in color])
            
            # 提取RGB
            r = color[0:2]
            g = color[2:4]
            b = color[4:6]
            
            # ASS使用BGR格式
            return f"00{b}{g}{r}".upper()
            
        except Exception:
            return "00FFFFFF"  # 默认白色


# 测试代码
if __name__ == "__main__":
    processor = VideoProcessor()
    
    # 测试获取视频信息
    test_video = "test.mp4"  # 替换为实际的测试视频路径
    if os.path.exists(test_video):
        info = processor.get_video_info(test_video)
        if info:
            print(f"视频信息: {info}")
    
    print("视频处理器初始化完成")
