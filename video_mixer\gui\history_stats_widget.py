#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史统计显示组件
显示素材使用统计、生成历史等信息
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QTableWidget, QTableWidgetItem, QPushButton,
    QGroupBox, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from datetime import datetime
from typing import Optional

try:
    from ..core.database import VideoGenerationDB
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.database import VideoGenerationDB

class HistoryStatsWidget(QWidget):
    """历史统计显示组件"""
    
    def __init__(self):
        super().__init__()
        self.db: Optional[VideoGenerationDB] = None
        self.init_ui()
        self.init_timer()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("📊 历史生成统计")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 总体统计组
        stats_group = QGroupBox("总体统计")
        stats_layout = QHBoxLayout()
        
        self.total_videos_label = QLabel("总视频: 0")
        self.total_materials_label = QLabel("总素材: 0")
        self.total_batches_label = QLabel("总批次: 0")
        
        stats_layout.addWidget(self.total_videos_label)
        stats_layout.addWidget(self.total_materials_label)
        stats_layout.addWidget(self.total_batches_label)
        stats_layout.addStretch()
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # 素材使用排行组
        usage_group = QGroupBox("素材使用排行 (Top 20)")
        usage_layout = QVBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新统计")
        refresh_btn.clicked.connect(self.refresh_stats)
        usage_layout.addWidget(refresh_btn)
        
        # 使用排行表
        self.usage_table = QTableWidget()
        self.usage_table.setColumnCount(4)
        self.usage_table.setHorizontalHeaderLabels(["素材名", "使用次数", "最后使用", "MD5"])
        
        # 设置列宽
        header = self.usage_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 素材名自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 使用次数
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 最后使用
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # MD5
        
        usage_layout.addWidget(self.usage_table)
        usage_group.setLayout(usage_layout)
        layout.addWidget(usage_group)
        
        # 操作按钮组
        button_layout = QHBoxLayout()
        
        self.clear_history_btn = QPushButton("🗑️ 清空历史记录")
        self.clear_history_btn.clicked.connect(self.clear_history)
        self.clear_history_btn.setStyleSheet("QPushButton { color: red; }")
        
        button_layout.addWidget(self.clear_history_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def init_timer(self):
        """初始化定时器，定期刷新统计"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_stats)
        self.refresh_timer.start(30000)  # 30秒刷新一次
    
    def connect_database(self):
        """连接数据库"""
        try:
            # 每次都重新创建连接，避免连接超时
            if self.db:
                try:
                    self.db.close()
                except:
                    pass
            self.db = VideoGenerationDB()
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def refresh_stats(self):
        """刷新统计信息"""
        if not self.connect_database():
            return
        
        try:
            # 获取总体统计
            stats = self.db.get_generation_stats()
            self.total_videos_label.setText(f"总视频: {stats.total_videos}")
            self.total_materials_label.setText(f"总素材: {stats.total_materials}")
            self.total_batches_label.setText(f"总批次: {stats.total_batches}")
            
            # 获取使用排行
            usage_data = self.db.get_material_usage_ranking(20)
            self.update_usage_table(usage_data)
            
        except Exception as e:
            print(f"❌ 刷新统计失败: {e}")
    
    def update_usage_table(self, usage_data):
        """更新使用排行表"""
        self.usage_table.setRowCount(len(usage_data))
        
        for row, (file_name, file_path, usage_count, last_used, md5_hash) in enumerate(usage_data):
            # 素材名
            name_item = QTableWidgetItem(file_name)
            name_item.setToolTip(file_path)  # 完整路径作为提示
            self.usage_table.setItem(row, 0, name_item)
            
            # 使用次数
            count_item = QTableWidgetItem(str(usage_count))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.usage_table.setItem(row, 1, count_item)
            
            # 最后使用时间
            if last_used:
                time_str = last_used.strftime("%m-%d %H:%M")
            else:
                time_str = "未使用"
            time_item = QTableWidgetItem(time_str)
            time_item.setTextAlignment(Qt.AlignCenter)
            self.usage_table.setItem(row, 2, time_item)
            
            # MD5（显示前8位）
            md5_item = QTableWidgetItem(md5_hash[:8] + "...")
            md5_item.setToolTip(md5_hash)  # 完整MD5作为提示
            md5_item.setTextAlignment(Qt.AlignCenter)
            self.usage_table.setItem(row, 3, md5_item)
            
            # 根据使用次数设置行颜色
            if usage_count == 0:
                # 未使用的素材用灰色
                for col in range(4):
                    item = self.usage_table.item(row, col)
                    if item:
                        item.setBackground(Qt.lightGray)
            elif usage_count >= 5:
                # 高频使用的素材用浅红色
                for col in range(4):
                    item = self.usage_table.item(row, col)
                    if item:
                        item.setBackground(Qt.red)
                        item.setForeground(Qt.white)
    
    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(
            self, 
            "确认清空", 
            "确定要清空所有历史记录吗？\n这将删除所有生成记录和使用统计，操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if not self.connect_database():
                return
            
            try:
                cursor = self.db.connection.cursor()
                
                # 清空所有历史表
                cursor.execute("DELETE FROM material_usage_stats")
                cursor.execute("DELETE FROM video_combinations")
                cursor.execute("DELETE FROM generation_batches")
                cursor.execute("DELETE FROM materials")
                
                self.db.connection.commit()
                cursor.close()
                
                QMessageBox.information(self, "清空完成", "历史记录已清空！")
                self.refresh_stats()
                
            except Exception as e:
                QMessageBox.critical(self, "清空失败", f"清空历史记录失败：{e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.db:
            self.db.close()
        event.accept()

if __name__ == "__main__":
    # 测试组件
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    widget = HistoryStatsWidget()
    widget.show()
    widget.refresh_stats()
    
    sys.exit(app.exec_())
