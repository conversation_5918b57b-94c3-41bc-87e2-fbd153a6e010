#!/usr/bin/env python3
"""
测试编码修复是否有效
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.video_splitter import VideoSplitter
from core.video_composer import VideoComposer

def test_encoding_fix():
    """测试编码修复"""
    print("测试编码修复...")

    try:
        # 初始化组件
        splitter = VideoSplitter(
            scene_threshold=1.0,
            min_segment_duration=1.0,
            fallback_segment_duration=3.0
        )
        composer = VideoComposer()

        print("✓ 组件初始化成功")
        print(f"  - 场景检测阈值: {splitter.scene_threshold}")
        print(f"  - 最短片段时长: {splitter.min_segment_duration}秒")
        print(f"  - 固定分割间隔: {splitter.fallback_segment_duration}秒")

        # 测试临时目录创建
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="video_test_")
        print(f"✓ 临时目录创建成功: {temp_dir}")

        # 测试一个简单的FFmpeg命令
        import subprocess

        # 测试FFmpeg是否可用
        try:
            kwargs = {
                'capture_output': True,
                'text': True,
                'encoding': 'utf-8',
                'errors': 'ignore',
                'timeout': 10
            }

            if os.name == 'nt':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

            result = subprocess.run(['ffmpeg', '-version'], **kwargs)
            if result.returncode == 0:
                print("✓ FFmpeg 可用")
            else:
                print("⚠ FFmpeg 不可用，但编码设置正确")

        except Exception as e:
            print(f"⚠ FFmpeg 测试失败: {e}")

        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir)
        print("✓ 临时目录清理完成")

        print("✓ 编码修复测试完成")
        return True

    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_encoding_fix()
    sys.exit(0 if success else 1)
