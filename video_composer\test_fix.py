#!/usr/bin/env python3
"""
测试编码修复是否有效
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.video_splitter import VideoSplitter
from core.video_composer import VideoComposer

def test_encoding_fix():
    """测试编码修复"""
    print("测试编码修复...")
    
    try:
        # 初始化组件
        splitter = VideoSplitter()
        composer = VideoComposer()
        
        print("✓ 组件初始化成功")
        
        # 测试一个简单的FFmpeg命令
        import subprocess
        
        # 测试FFmpeg是否可用
        try:
            kwargs = {
                'capture_output': True,
                'text': True,
                'encoding': 'utf-8',
                'errors': 'ignore',
                'timeout': 10
            }
            
            if os.name == 'nt':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            
            result = subprocess.run(['ffmpeg', '-version'], **kwargs)
            if result.returncode == 0:
                print("✓ FFmpeg 可用")
            else:
                print("⚠ FFmpeg 不可用，但编码设置正确")
                
        except Exception as e:
            print(f"⚠ FFmpeg 测试失败: {e}")
        
        print("✓ 编码修复测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_encoding_fix()
    sys.exit(0 if success else 1)
