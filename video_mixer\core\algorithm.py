#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匹配算法模块
Matching Algorithm Module
"""

import random
import numpy as np
import os
import re
from typing import List, Dict, Any, Tuple, Optional, Set
from dataclasses import dataclass
from collections import defaultdict
import hashlib
try:
    from .subtitle_parser import SubtitleItem
    from .video_processor import VideoInfo
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.subtitle_parser import SubtitleItem
    from core.video_processor import VideoInfo


@dataclass
class VideoCandidate:
    """视频候选项"""
    video_info: VideoInfo
    source_index: int
    speed_scale: float
    size_scale: float
    new_duration: float  # 调整速度后的时长


@dataclass
class MatchResult:
    """匹配结果"""
    subtitle: SubtitleItem
    video_candidate: VideoCandidate
    start_offset: float  # 在原视频中的开始位置（秒）
    adjusted_duration: Optional[float] = None  # 调整后的时长（秒），用于时长随机化


class VideoMatcher:
    """视频匹配算法"""
    
    def __init__(self):
        self.min_speed = 0.9
        self.max_speed = 1.0
        self.max_scale = 1.0
        self.repeat_scale = 1
        self.output_width = 1080
        self.output_height = 1440
    
    def set_parameters(
        self,
        min_speed: float = 0.9,
        max_speed: float = 1.0,
        max_scale: float = 1.0,
        repeat_scale: int = 1,
        output_width: int = 1080,
        output_height: int = 1440
    ):
        """设置匹配参数"""
        self.min_speed = min_speed
        self.max_speed = max_speed
        self.max_scale = max_scale
        self.repeat_scale = repeat_scale
        self.output_width = output_width
        self.output_height = output_height
    
    def compute_speed_scales(self) -> List[float]:
        """计算速度比例列表"""
        if self.repeat_scale <= 1:
            return [self.min_speed]
        
        diff = self.max_speed - self.min_speed
        unit = diff / (self.repeat_scale - 1)
        
        scales = []
        current = self.min_speed
        
        for i in range(self.repeat_scale - 1):
            scales.append(round(current, 4))
            current += unit
        
        scales.append(self.max_speed)
        
        # 去重并排序
        return sorted(list(set(scales)))
    
    def compute_size_scales(self) -> List[float]:
        """计算缩放比例列表"""
        if self.repeat_scale <= 1:
            return [1.0]
        
        diff = self.max_scale - 1.0
        unit = diff / (self.repeat_scale - 1)
        
        scales = []
        current = 1.0
        
        for i in range(self.repeat_scale - 1):
            scales.append(round(current, 4))
            current += unit
        
        scales.append(self.max_scale)
        
        # 去重并排序
        return sorted(list(set(scales)))
    
    def validate_videos(self, videos: List[VideoInfo]) -> List[str]:
        """验证视频素材，返回错误信息列表"""
        errors = []
        
        for i, video in enumerate(videos):
            if not video.width or not video.height or not video.duration:
                errors.append(f"视频素材 {video.path} 信息不完整")
                continue
            
            if video.width < self.output_width:
                errors.append(f"视频素材 {video.path} 宽度({video.width})小于输出宽度({self.output_width})")
            
            if video.height < self.output_height:
                errors.append(f"视频素材 {video.path} 高度({video.height})小于输出高度({self.output_height})")
        
        return errors
    
    def generate_video_candidates(self, videos: List[VideoInfo]) -> List[List[VideoCandidate]]:
        """为每个视频生成候选项列表"""
        speed_scales = self.compute_speed_scales()
        size_scales = self.compute_size_scales()
        
        candidates_by_video = []
        
        for video_index, video in enumerate(videos):
            video_candidates = []
            
            # 随机打乱缩放比例
            shuffled_size_scales = size_scales.copy()
            random.shuffle(shuffled_size_scales)
            
            for speed_index, speed_scale in enumerate(speed_scales):
                # 计算调整速度后的时长
                new_duration = video.duration / speed_scale
                
                # 选择对应的缩放比例
                size_scale = shuffled_size_scales[speed_index % len(shuffled_size_scales)]
                
                candidate = VideoCandidate(
                    video_info=video,
                    source_index=video_index,
                    speed_scale=speed_scale,
                    size_scale=size_scale,
                    new_duration=new_duration
                )
                
                video_candidates.append(candidate)
            
            if video_candidates:
                candidates_by_video.append(video_candidates)
        
        return candidates_by_video
    
    def compute_generation_count(
        self,
        subtitles: List[SubtitleItem],
        videos: List[VideoInfo]
    ) -> Tuple[int, List[List[MatchResult]]]:
        """计算可生成的视频数量和匹配方案"""

        # 设置固定随机种子，确保每次计算结果稳定
        import random
        random.seed(42)

        # 验证参数
        if not subtitles:
            return 0, []

        if not videos:
            return 0, []
        
        # 验证视频素材
        errors = self.validate_videos(videos)
        if errors:
            print(f"视频验证失败: {errors}")
            return 0, []
        
        # 生成视频候选项
        candidates_by_video = self.generate_video_candidates(videos)
        if not candidates_by_video:
            return 0, []
        
        # 计算理论最大生成数量
        # 如果字幕数量 > 素材数量，无法生成视频（因为单个视频内素材不能重复）
        if len(subtitles) > len(videos):
            print(f"字幕数量({len(subtitles)})大于素材数量({len(videos)})，无法生成视频")
            return 0, []
        
        # 理论最大生成数量 = 素材数量 × 重复阈值 ÷ 字幕数量
        max_possible_videos = (len(videos) * self.repeat_scale) // len(subtitles)
        
        print(f"素材数量: {len(videos)}, 字幕数量: {len(subtitles)}, 重复阈值: {self.repeat_scale}")
        print(f"理论最大生成数量: {max_possible_videos}")
        
        if max_possible_videos == 0:
            return 0, []
        
        # 全局素材使用计数器 {video_index: 使用次数}
        global_usage_count = {}
        for i in range(len(videos)):
            global_usage_count[i] = 0
        
        # 存储所有生成的视频方案
        all_results = []
        generated_video_hashes = set()
        
        # 尝试生成视频
        max_attempts = min(max_possible_videos * 20, 2000)
        consecutive_failures = 0
        
        for attempt in range(max_attempts):
            video_result = self._generate_single_video_fixed(
                subtitles, 
                videos,
                candidates_by_video, 
                global_usage_count
            )
            
            if video_result:
                # 检查视频是否重复
                video_hash = self._get_video_hash(video_result)
                if video_hash not in generated_video_hashes:
                    all_results.append(video_result)
                    generated_video_hashes.add(video_hash)
                    
                    # 更新全局使用计数
                    self._update_global_usage_fixed(video_result, global_usage_count)
                    
                    print(f"成功生成第 {len(all_results)} 个视频方案")
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
            else:
                consecutive_failures += 1
            
            # 如果达到理论最大值，停止尝试
            if len(all_results) >= max_possible_videos:
                break
                
            # 如果连续失败太多次，提前结束
            if consecutive_failures > 100:
                print(f"连续失败{consecutive_failures}次，提前结束")
                break
        
        return len(all_results), all_results

    def _generate_single_video_fixed(
        self,
        subtitles: List[SubtitleItem],
        videos: List[VideoInfo],
        candidates_by_video: List[List[VideoCandidate]],
        global_usage_count: Dict[int, int]
    ) -> Optional[List[MatchResult]]:
        """生成单个视频的匹配方案（修复版）"""
        
        # 随机打乱字幕顺序
        shuffled_subtitles = subtitles.copy()
        random.shuffle(shuffled_subtitles)
        
        current_result = []
        used_video_indices = set()  # 当前视频中已使用的视频索引
        
        for subtitle in shuffled_subtitles:
            # 获取可用的视频素材
            available_options = []
            
            for video_index, candidates in enumerate(candidates_by_video):
                # 检查是否在当前视频中已使用
                if video_index in used_video_indices:
                    continue
                
                # 检查全局使用次数是否超过阈值
                current_usage = global_usage_count.get(video_index, 0)
                if current_usage >= self.repeat_scale:
                    continue
                
                # 检查是否有合适的候选项
                for candidate in candidates:
                    if subtitle.duration_ms <= (candidate.new_duration * 1000):
                        available_options.append((video_index, candidate))
        
            if not available_options:
                return None  # 无法为当前字幕找到匹配
        
            # 随机选择一个可用选项
            video_index, candidate = random.choice(available_options)
            used_video_indices.add(video_index)
            
            # 计算随机开始位置
            max_start_time = candidate.new_duration - (subtitle.duration_ms / 1000)
            start_offset = random.uniform(0, max(0, max_start_time))
            
            match_result = MatchResult(
                subtitle=subtitle,
                video_candidate=candidate,
                start_offset=start_offset
            )
            
            current_result.append(match_result)
        
        # 检查是否所有字幕都匹配成功
        if len(current_result) == len(shuffled_subtitles):
            return current_result
        
        return None

    def _get_video_hash(self, video_result: List[MatchResult]) -> str:
        """生成视频的唯一标识哈希"""
        # 按字幕时间排序，确保相同内容的视频有相同哈希
        sorted_result = sorted(video_result, key=lambda x: x.subtitle.start_ms)
        
        hash_parts = []
        for match in sorted_result:
            # 使用视频路径、速度、缩放、开始时间等生成哈希
            part = (f"{match.video_candidate.video_info.path}_"
                    f"{match.video_candidate.speed_scale}_"
                    f"{match.video_candidate.size_scale}_"
                    f"{match.start_offset:.2f}_"
                    f"{match.subtitle.start_ms}")
            hash_parts.append(part)
        
        import hashlib
        return hashlib.md5("_".join(hash_parts).encode()).hexdigest()

    def _update_global_usage_fixed(
        self, 
        video_result: List[MatchResult], 
        global_usage_count: Dict[int, int]
    ):
        """更新全局使用计数（修复版）"""
        # 记录本次视频中使用的素材
        used_video_indices = set()
        
        for match in video_result:
            video_path = match.video_candidate.video_info.path
            
            # 找到对应的视频索引
            for video_index, candidates in enumerate(self.generate_video_candidates([match.video_candidate.video_info])):
                if candidates and len(candidates) > 0:
                    if candidates[0].video_info.path == video_path:
                        used_video_indices.add(video_index)
                        break
        
        # 更新使用计数
        for video_index in used_video_indices:
            global_usage_count[video_index] = global_usage_count.get(video_index, 0) + 1

    def _generate_single_attempt(
        self,
        subtitles: List[SubtitleItem],
        candidates_by_video: List[List[VideoCandidate]],
        min_subtitle_duration: int
    ) -> List[List[MatchResult]]:
        """单次匹配尝试 - 已废弃，使用新的逻辑"""
        # 这个方法保留以避免其他地方的调用错误，但实际不再使用
        return []



    def get_random_segment(self, total_duration: float, segment_duration: float) -> Tuple[float, float]:
        """获取随机时间段"""
        if segment_duration > total_duration:
            raise ValueError("片段长度不能大于总长度")
        
        max_start = total_duration - segment_duration
        start = random.uniform(0, max(0, max_start))
        end = start + segment_duration
        
        return start, end
    
    def analyze_material_usage(self, results: List[List[MatchResult]]) -> Dict[str, int]:
        """分析素材使用情况"""
        usage_count = {}
        
        for result_group in results:
            for match_result in result_group:
                video_path = match_result.video_candidate.video_info.path
                usage_count[video_path] = usage_count.get(video_path, 0) + 1
        
        return usage_count
    
    def print_generation_stats(
        self,
        subtitles: List[SubtitleItem],
        videos: List[VideoInfo],
        results: List[List[MatchResult]]
    ):
        """打印生成统计信息"""
        print(f"\n=== 视频生成统计 ===")
        print(f"字幕数量: {len(subtitles)}")
        print(f"视频素材数量: {len(videos)}")
        print(f"可生成视频数量: {len(results)}")
        
        if results:
            # 素材使用统计
            usage = self.analyze_material_usage(results)
            print(f"\n素材使用情况:")
            for video_path, count in usage.items():
                print(f"  {video_path}: {count}次")
            
            # 参数统计
            speed_scales = set()
            size_scales = set()
            
            for result_group in results:
                for match_result in result_group:
                    speed_scales.add(match_result.video_candidate.speed_scale)
                    size_scales.add(match_result.video_candidate.size_scale)
            
            print(f"\n使用的速度比例: {sorted(speed_scales)}")
            print(f"使用的缩放比例: {sorted(size_scales)}")

    def _build_candidate_mapping(self, candidates_by_video: List[List[VideoCandidate]]) -> Dict[str, Tuple[int, int]]:
        """构建候选项到索引的映射"""
        mapping = {}
        for video_index, candidates in enumerate(candidates_by_video):
            for candidate_index, candidate in enumerate(candidates):
                key = f"{candidate.video_info.path}_{candidate.speed_scale}_{candidate.size_scale}"
                mapping[key] = (video_index, candidate_index)
        return mapping

    def _update_global_usage_optimized(
        self, 
        video_result: List[MatchResult], 
        global_usage_count: Dict[Tuple[int, int], int],
        candidate_mapping: Dict[str, Tuple[int, int]]
    ):
        """优化的全局使用计数更新"""
        for match in video_result:
            key = f"{match.video_candidate.video_info.path}_{match.video_candidate.speed_scale}_{match.video_candidate.size_scale}"
            if key in candidate_mapping:
                candidate_key = candidate_mapping[key]
                global_usage_count[candidate_key] = global_usage_count.get(candidate_key, 0) + 1


@dataclass
class VideoSegment:
    """视频片段信息"""
    video_index: int  # 素材索引
    position: int     # 在混剪视频中的位置 (0-3)
    start_time: float # 在原素材中的开始时间
    duration: float   # 片段时长

    def __hash__(self):
        return hash((self.video_index, self.position, round(self.start_time, 2), round(self.duration, 2)))


@dataclass
class GeneratedVideo:
    """生成的混剪视频"""
    segments: List[VideoSegment]
    video_id: str

    def get_content_hash(self) -> str:
        """获取内容哈希，用于重复检测"""
        content_parts = []
        for seg in sorted(self.segments, key=lambda x: x.position):
            content_parts.append(f"{seg.video_index}_{seg.position}_{seg.start_time:.2f}_{seg.duration:.2f}")
        return hashlib.md5("_".join(content_parts).encode()).hexdigest()


class OptimizedVideoGenerator:
    """优化的视频生成器 - 实现混合策略"""

    def __init__(self, num_materials: int = 100, segments_per_video: int = 4, enable_similarity_check: bool = False, optimization_mode: str = "balanced"):
        self.num_materials = num_materials
        self.segments_per_video = segments_per_video
        self.base_duplicate_threshold = 0.20  # 基础20%重复率阈值
        self.enable_similarity_check = enable_similarity_check  # 是否启用相似性检测
        self.optimization_mode = optimization_mode  # 优化模式：quality/balanced/quantity/fast

        # 快速模式：当素材数量大时自动启用
        self.fast_mode = (num_materials >= 40) or (optimization_mode == "fast")
        if self.fast_mode:
            print(f"🚀 启用快速模式（{num_materials}个素材）")

        # 数据库连接（用于重复检查）
        self.database_manager = None
        self.material_md5s = []  # 素材MD5列表
        self.enable_database_check = True  # 是否启用数据库重复检查

        # 根据优化模式设置动态重复率参数
        if optimization_mode == "quality":
            # 质量优先：更严格的阈值
            self.early_phase_threshold = 0.17
            self.late_phase_threshold = 0.20
            self.transition_point = 0.7
        elif optimization_mode == "quantity":
            # 数量优先：更宽松的阈值
            self.early_phase_threshold = 0.21
            self.late_phase_threshold = 0.25
            self.transition_point = 0.4
        elif optimization_mode == "fast":
            # 快速模式：速度优先，适度放宽阈值
            self.early_phase_threshold = 0.20
            self.late_phase_threshold = 0.28
            self.transition_point = 0.3
        else:  # balanced
            # 平衡模式：适中的阈值
            self.early_phase_threshold = 0.19
            self.late_phase_threshold = 0.23
            self.transition_point = 0.5

        # 策略参数
        self.phase1_threshold = 0.7  # 第一阶段结束阈值（使用均匀度）
        self.phase2_threshold = 100   # 第二阶段最小可选组合数
        self.max_consecutive_failures = 50

        # 状态跟踪
        self.usage_matrix = np.zeros((num_materials, segments_per_video), dtype=int)
        self.generated_videos: List[GeneratedVideo] = []
        self.current_phase = 1

        # 性能优化缓存
        self._duplicate_rate_cache = {}  # 缓存重复率计算结果
        self._material_overlap_cache = {}  # 缓存素材重叠计算结果
        self._cache_hit_count = 0  # 缓存命中计数
        self._cache_miss_count = 0  # 缓存未命中计数

        # 素材相似性矩阵（缓存）- 仅在启用时使用
        self.similarity_matrix: Optional[np.ndarray] = None
        self.material_paths: List[str] = []

        # 兼容性属性（向后兼容）
        self.duplicate_threshold = self.base_duplicate_threshold

    def reset(self):
        """重置生成器状态"""
        self.usage_matrix = np.zeros((self.num_materials, self.segments_per_video), dtype=int)
        self.generated_videos = []
        self.current_phase = 1

    def get_dynamic_duplicate_threshold(self, max_videos: int) -> float:
        """获取动态重复率阈值"""
        current_count = len(self.generated_videos)
        progress = current_count / max_videos if max_videos > 0 else 0

        if progress < self.transition_point:
            # 前期使用严格阈值
            return self.early_phase_threshold
        else:
            # 后期逐渐放宽到晚期阈值
            transition_progress = (progress - self.transition_point) / (1.0 - self.transition_point)
            return self.early_phase_threshold + (self.late_phase_threshold - self.early_phase_threshold) * transition_progress

    def apply_advanced_duration_randomization(self, segments: List[VideoSegment]) -> List[VideoSegment]:
        """高级多层次时间随机化"""
        import math

        randomized_segments = []
        total_segments = len(segments)

        for i, segment in enumerate(segments):
            # 1. 基础随机化（±15%）
            base_variation = random.uniform(-0.15, 0.15)

            # 2. 位置相关随机化（不同位置不同变化模式）
            position_factor = math.sin(i * math.pi / total_segments) * 0.08  # ±8%额外变化

            # 3. 视频长度相关随机化
            length_factor = (segment.duration - 2.0) / 10.0 * 0.05  # 基于时长的微调

            # 4. 随机噪声（破坏规律性）
            noise_factor = random.uniform(-0.03, 0.03)

            # 综合变化（限制总变化范围）
            total_variation = base_variation + position_factor + length_factor + noise_factor
            total_variation = max(-0.25, min(0.25, total_variation))  # 限制在±25%

            # 应用变化
            new_duration = segment.duration * (1 + total_variation)
            new_duration = max(1.0, min(new_duration, segment.duration * 1.3))  # 安全范围

            # 创建新的片段
            new_segment = VideoSegment(
                video_index=segment.video_index,
                position=segment.position,
                start_time=segment.start_time,
                duration=new_duration
            )

            randomized_segments.append(new_segment)

        return randomized_segments

    def calculate_anti_detection_score(self, material_idx: int, position: int, existing_videos: List[GeneratedVideo]) -> float:
        """计算素材的反检测分数（分数越高越不容易被检测）"""
        score = 0.0

        # 1. 使用频率分数（使用越少分数越高）
        usage_count = self.usage_matrix[material_idx, position]
        max_usage = self.usage_matrix[:, position].max() if self.usage_matrix.size > 0 else 0
        frequency_score = (max_usage - usage_count + 1) / (max_usage + 1)
        score += frequency_score * 0.4

        # 2. 位置多样性分数（在不同位置使用过的素材分数更高）
        position_usage = self.usage_matrix[material_idx, :]
        used_positions = np.count_nonzero(position_usage)
        diversity_score = used_positions / self.segments_per_video
        score += diversity_score * 0.3

        # 3. 时间间隔分数（与最近使用的时间间隔越长分数越高）
        recent_usage_penalty = 0.0
        for i, video in enumerate(existing_videos[-5:]):  # 只检查最近5个视频
            for segment in video.segments:
                if segment.video_index == material_idx and segment.position == position:
                    # 越近的使用惩罚越大
                    recent_usage_penalty += (5 - i) * 0.1

        time_score = max(0, 1.0 - recent_usage_penalty)
        score += time_score * 0.3

        return score

    def get_material_usage_stats(self) -> Dict[int, int]:
        """获取素材使用统计"""
        usage_stats = {}
        for video in self.generated_videos:
            for segment in video.segments:
                material_id = segment.video_index
                usage_stats[material_id] = usage_stats.get(material_id, 0) + 1
        return usage_stats

    def intelligent_material_selection(self, available_materials: List[int], position: int) -> int:
        """智能素材选择策略（优化素材分布均衡）"""
        if not available_materials:
            return None

        if len(available_materials) == 1:
            return available_materials[0]

        # 获取全局使用统计
        global_usage_stats = self.get_material_usage_stats()

        # 计算每个素材的综合分数
        scores = {}
        for material in available_materials:
            # 1. 反检测分数（原有逻辑）
            anti_detection_score = self.calculate_anti_detection_score(material, position, self.generated_videos)

            # 2. 使用均衡分数（新增）
            global_usage = global_usage_stats.get(material, 0)
            max_usage = max(global_usage_stats.values()) if global_usage_stats else 0

            # 使用次数越少，分数越高
            if max_usage > 0:
                balance_score = (max_usage - global_usage) / max_usage
            else:
                balance_score = 1.0

            # 3. 位置使用频率分数
            position_usage = self.usage_matrix[material, position]
            position_score = 1.0 / (1.0 + position_usage * 0.5)

            # 综合分数：反检测(60%) + 使用均衡(30%) + 位置频率(10%)
            total_score = (anti_detection_score * 0.6 +
                          balance_score * 0.3 +
                          position_score * 0.1)

            scores[material] = total_score

        # 选择分数最高的前5个素材（增加候选数量）
        sorted_materials = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        top_materials = [m[0] for m in sorted_materials[:min(5, len(sorted_materials))]]

        # 从前5个中加权随机选择（分数越高，被选中概率越大）
        top_scores = [scores[m] for m in top_materials]
        total_score = sum(top_scores)

        if total_score > 0:
            weights = [s / total_score for s in top_scores]
            return random.choices(top_materials, weights=weights)[0]
        else:
            return random.choice(top_materials)

    def calculate_duplicate_rate(self, video1: GeneratedVideo, video2: GeneratedVideo) -> float:
        """计算两个视频的连续重复率（基于时间轴）"""
        if len(video1.segments) != len(video2.segments):
            return 0.0

        # 构建时间轴表示
        timeline1 = self._build_video_timeline(video1)
        timeline2 = self._build_video_timeline(video2)

        # 计算连续重复时长
        continuous_duplicate_duration = self._calculate_continuous_duplicate(timeline1, timeline2)

        # 计算总时长
        total_duration = sum(seg.duration for seg in video1.segments)

        if total_duration == 0:
            return 0.0

        return continuous_duplicate_duration / total_duration

    def _build_video_timeline(self, video: GeneratedVideo) -> List[tuple]:
        """构建视频的时间轴表示
        返回: [(start_time, end_time, video_index, source_start, source_end), ...]
        """
        timeline = []
        current_time = 0.0

        # 按位置排序片段
        sorted_segments = sorted(video.segments, key=lambda x: x.position)

        for segment in sorted_segments:
            start_time = current_time
            end_time = current_time + segment.duration
            source_start = segment.start_time
            source_end = segment.start_time + segment.duration

            timeline.append((start_time, end_time, segment.video_index, source_start, source_end))
            current_time = end_time

        return timeline

    def _calculate_continuous_duplicate(self, timeline1: List[tuple], timeline2: List[tuple]) -> float:
        """计算两个时间轴的连续重复时长"""
        total_duplicate = 0.0

        # 遍历timeline1的每个片段
        for t1_start, t1_end, t1_video, t1_src_start, t1_src_end in timeline1:
            # 遍历timeline2的每个片段
            for t2_start, t2_end, t2_video, t2_src_start, t2_src_end in timeline2:
                # 只检查相同素材的片段
                if t1_video == t2_video:
                    # 计算源素材时间重叠
                    src_overlap_start = max(t1_src_start, t2_src_start)
                    src_overlap_end = min(t1_src_end, t2_src_end)

                    if src_overlap_start < src_overlap_end:
                        # 有源素材重叠，计算在最终视频中的重叠时长
                        overlap_duration = src_overlap_end - src_overlap_start

                        # 考虑在最终视频时间轴中的实际重叠
                        # 这里简化处理：如果源素材有重叠，就认为整个片段重叠
                        if overlap_duration > 0.5:  # 至少0.5秒重叠才算
                            # 计算实际重叠的时长（取较短的片段时长）
                            actual_overlap = min(t1_end - t1_start, t2_end - t2_start)
                            total_duplicate += actual_overlap

        return total_duplicate

    def _calculate_time_overlap(self, seg1: VideoSegment, seg2: VideoSegment) -> float:
        """计算两个片段的时间重叠率"""
        start1, end1 = seg1.start_time, seg1.start_time + seg1.duration
        start2, end2 = seg2.start_time, seg2.start_time + seg2.duration

        overlap_start = max(start1, start2)
        overlap_end = min(end1, end2)

        if overlap_start >= overlap_end:
            return 0.0

        overlap_duration = overlap_end - overlap_start
        min_duration = min(seg1.duration, seg2.duration)

        return overlap_duration / min_duration if min_duration > 0 else 0.0

    def calculate_material_time_overlap_rate(self, video1: GeneratedVideo, video2: GeneratedVideo) -> float:
        """计算素材+时间片段的综合重叠率"""
        if len(video1.segments) != len(video2.segments):
            return 0.0

        # 获取两个视频的素材-时间片段组合
        segments1 = [(seg.video_index, seg.start_time, seg.start_time + seg.duration)
                     for seg in video1.segments]
        segments2 = [(seg.video_index, seg.start_time, seg.start_time + seg.duration)
                     for seg in video2.segments]

        total_overlap_score = 0.0
        total_segments = len(segments1)

        for seg1 in segments1:
            material1, start1, end1 = seg1
            for seg2 in segments2:
                material2, start2, end2 = seg2

                # 如果是相同素材
                if material1 == material2:
                    # 计算时间重叠
                    overlap_start = max(start1, start2)
                    overlap_end = min(end1, end2)

                    if overlap_start < overlap_end:
                        # 有时间重叠，计算重叠程度
                        overlap_duration = overlap_end - overlap_start
                        seg1_duration = end1 - start1
                        seg2_duration = end2 - start2
                        min_duration = min(seg1_duration, seg2_duration)

                        if min_duration > 0:
                            # 重叠分数 = 时间重叠比例
                            overlap_score = overlap_duration / min_duration
                            total_overlap_score += overlap_score
                            break  # 找到重叠就跳出内层循环
                    else:
                        # 相同素材但不同时间片段，给予部分重叠分数
                        total_overlap_score += 0.3  # 30%的基础重叠分数
                        break

        # 返回平均重叠率
        return total_overlap_score / total_segments if total_segments > 0 else 0.0

    def is_video_valid(self, new_video: GeneratedVideo, max_videos: int = 200) -> bool:
        """检查新视频是否满足重复率要求（高性能版 + 数据库检查）"""
        # 0. 数据库重复检查（最优先）
        if self.check_combination_in_database(new_video):
            return False

        # 1. 快速预检查：素材多样性（最快的检查）
        if not self._check_material_diversity(new_video.segments):
            return False

        # 2. 快速素材重叠预筛选
        new_materials = set(seg.video_index for seg in new_video.segments)
        material_time_threshold = 0.30
        current_threshold = self.get_dynamic_duplicate_threshold(max_videos)

        # 根据模式选择检查策略
        if self.fast_mode:
            # 快速模式：大幅减少检查数量
            check_count = min(10, len(self.generated_videos))  # 最多检查10个
            videos_to_check = self.generated_videos[-check_count:]  # 只检查最近的
        else:
            # 标准模式：检查最近20个 + 随机抽样
            check_count = min(20, len(self.generated_videos))
            videos_to_check = self.generated_videos[-check_count:]

        for existing_video in videos_to_check:
            # 快速素材重叠检查
            existing_materials = set(seg.video_index for seg in existing_video.segments)
            material_overlap = len(new_materials.intersection(existing_materials)) / len(new_materials)

            # 根据模式调整跳过阈值
            skip_threshold = 0.4 if self.fast_mode else 0.3
            if material_overlap < skip_threshold:
                continue

            # 只有素材重叠较多时才进行详细计算
            duplicate_rate = self.calculate_duplicate_rate(new_video, existing_video)
            if duplicate_rate > current_threshold:
                return False

            # 快速模式下跳过素材+时间重叠检查（最耗时的部分）
            if not self.fast_mode:
                material_time_overlap = self.calculate_material_time_overlap_rate(new_video, existing_video)
                if material_time_overlap > material_time_threshold:
                    return False

        # 非快速模式下才进行额外的随机抽样检查
        if not self.fast_mode and len(self.generated_videos) > 20:
            import random
            remaining_videos = self.generated_videos[:-20]
            sample_size = min(5, len(remaining_videos))  # 减少抽样数量
            sampled_videos = random.sample(remaining_videos, sample_size)

            for existing_video in sampled_videos:
                existing_materials = set(seg.video_index for seg in existing_video.segments)
                material_overlap = len(new_materials.intersection(existing_materials)) / len(new_materials)

                if material_overlap < 0.3:
                    continue

                duplicate_rate = self.calculate_duplicate_rate(new_video, existing_video)
                if duplicate_rate > current_threshold:
                    return False

        return True

    def set_database_manager(self, database_manager, material_md5s: List[str]):
        """设置数据库管理器和素材MD5列表"""
        self.database_manager = database_manager
        self.material_md5s = material_md5s
        print(f"🔗 已连接数据库，启用重复组合检查")

    def check_combination_in_database(self, video: GeneratedVideo) -> bool:
        """检查组合是否在数据库中已存在"""
        if not self.database_manager or not self.material_md5s or not self.enable_database_check:
            return False  # 未启用数据库检查，认为不重复

        try:
            # 获取视频中使用的素材MD5和位置
            material_md5s = []
            positions = []

            for segment in video.segments:
                if segment.video_index < len(self.material_md5s):
                    material_md5s.append(self.material_md5s[segment.video_index])
                    positions.append(segment.position)
                else:
                    # 素材索引超出范围，跳过数据库检查
                    return False

            # 检查组合是否已存在
            is_used = self.database_manager.is_combination_used(material_md5s, positions)
            if is_used:
                print(f"🔍 数据库检查：组合已存在，跳过")

            return is_used

        except Exception as e:
            print(f"⚠️ 数据库检查失败：{e}")
            return False  # 检查失败时不阻止生成

    def get_usage_uniformity(self) -> float:
        """计算素材使用的均匀度 (0-1，1表示完全均匀)"""
        if self.usage_matrix.sum() == 0:
            return 1.0

        # 计算每个素材-位置组合的使用次数方差
        usage_flat = self.usage_matrix.flatten()
        mean_usage = usage_flat.mean()
        variance = np.var(usage_flat)

        # 归一化方差，转换为均匀度
        max_possible_variance = mean_usage ** 2
        if max_possible_variance == 0:
            return 1.0

        uniformity = 1.0 - (variance / max_possible_variance)
        return max(0.0, min(1.0, uniformity))

    def phase1_position_rotation_strategy(self) -> Optional[GeneratedVideo]:
        """第一阶段：位置轮换策略"""
        segments = []
        used_materials = set()

        for position in range(self.segments_per_video):
            # 找到在当前位置使用次数最少的素材
            available_materials = []
            min_usage = float('inf')

            for material_idx in range(self.num_materials):
                if material_idx in used_materials:
                    continue

                usage_count = self.usage_matrix[material_idx, position]
                if usage_count < min_usage:
                    min_usage = usage_count
                    available_materials = [material_idx]
                elif usage_count == min_usage:
                    available_materials.append(material_idx)

            if not available_materials:
                return None

            # 使用智能选择策略
            selected_material = self.intelligent_material_selection(available_materials, position)
            if selected_material is None:
                return None
            used_materials.add(selected_material)

            # 生成随机时间参数
            start_time = random.uniform(0, 5)  # 假设素材长度足够
            duration = random.uniform(2, 3)    # 2-3秒片段

            segment = VideoSegment(
                video_index=selected_material,
                position=position,
                start_time=start_time,
                duration=duration
            )
            segments.append(segment)

        video_id = f"video_{len(self.generated_videos) + 1}"
        return GeneratedVideo(segments=segments, video_id=video_id)

    def phase2_conflict_minimization_strategy(self) -> Optional[GeneratedVideo]:
        """第二阶段：冲突最小化策略"""
        best_video = None
        min_conflict_score = float('inf')
        max_attempts = 100

        for _ in range(max_attempts):
            segments = []
            used_materials = set()

            for position in range(self.segments_per_video):
                # 获取可用素材（排除已使用的）
                available_materials = [i for i in range(self.num_materials)
                                     if i not in used_materials]

                if not available_materials:
                    break

                # 计算每个素材的冲突分数
                material_scores = []
                for material_idx in available_materials:
                    # 基础分数：使用频率
                    usage_score = self.usage_matrix[material_idx, position]

                    # 稀缺性分数：该素材在其他位置的可用性
                    scarcity_score = 0
                    for other_pos in range(self.segments_per_video):
                        if other_pos != position:
                            scarcity_score += self.usage_matrix[material_idx, other_pos]

                    # 综合分数（越低越好）
                    total_score = usage_score * 2 + scarcity_score * 0.5
                    material_scores.append((material_idx, total_score))

                # 从冲突分数最低的素材中智能选择
                material_scores.sort(key=lambda x: x[1])
                low_conflict_materials = [m[0] for m in material_scores[:min(5, len(material_scores))]]
                selected_material = self.intelligent_material_selection(low_conflict_materials, position)
                if selected_material is None:
                    break
                used_materials.add(selected_material)

                # 生成时间参数
                start_time = random.uniform(0, 5)
                duration = random.uniform(2, 3)

                segment = VideoSegment(
                    video_index=selected_material,
                    position=position,
                    start_time=start_time,
                    duration=duration
                )
                segments.append(segment)

            if len(segments) == self.segments_per_video:
                video_id = f"video_{len(self.generated_videos) + 1}"
                candidate_video = GeneratedVideo(segments=segments, video_id=video_id)

                # 计算冲突分数
                conflict_score = self._calculate_conflict_score(candidate_video)

                if conflict_score < min_conflict_score:
                    min_conflict_score = conflict_score
                    best_video = candidate_video

        return best_video

    def _calculate_conflict_score(self, video: GeneratedVideo) -> float:
        """计算视频的冲突分数"""
        if not self.generated_videos:
            return 0.0

        total_conflict = 0.0
        for existing_video in self.generated_videos:
            duplicate_rate = self.calculate_duplicate_rate(video, existing_video)
            total_conflict += duplicate_rate

        return total_conflict / len(self.generated_videos)

    def phase3_greedy_strategy(self) -> Optional[GeneratedVideo]:
        """第三阶段：贪心策略"""
        max_attempts = 200

        for _ in range(max_attempts):
            segments = []
            used_materials = set()

            for position in range(self.segments_per_video):
                available_materials = [i for i in range(self.num_materials)
                                     if i not in used_materials]

                if not available_materials:
                    break

                # 使用智能选择策略
                selected_material = self.intelligent_material_selection(available_materials, position)
                if selected_material is None:
                    break
                used_materials.add(selected_material)

                start_time = random.uniform(0, 5)
                duration = random.uniform(2, 3)

                segment = VideoSegment(
                    video_index=selected_material,
                    position=position,
                    start_time=start_time,
                    duration=duration
                )
                segments.append(segment)

            if len(segments) == self.segments_per_video:
                video_id = f"video_{len(self.generated_videos) + 1}"
                candidate_video = GeneratedVideo(segments=segments, video_id=video_id)

                # 直接检查是否有效
                if self.is_video_valid(candidate_video, 200):  # 使用默认最大值
                    return candidate_video

        return None

    def update_usage_matrix(self, video: GeneratedVideo):
        """更新使用矩阵"""
        for segment in video.segments:
            self.usage_matrix[segment.video_index, segment.position] += 1

    def determine_current_phase(self) -> int:
        """确定当前应该使用的策略阶段"""
        if len(self.generated_videos) == 0:
            return 1

        # 检查是否应该进入第二阶段
        uniformity = self.get_usage_uniformity()
        if uniformity < self.phase1_threshold:
            # 检查是否应该进入第三阶段
            available_combinations = self._estimate_available_combinations()
            if available_combinations < self.phase2_threshold:
                return 3
            return 2

        return 1

    def _estimate_available_combinations(self) -> int:
        """估算剩余可用组合数量"""
        # 简化估算：计算每个位置上使用次数较少的素材数量
        available_count = 1
        for position in range(self.segments_per_video):
            position_usage = self.usage_matrix[:, position]
            max_usage = position_usage.max() if len(position_usage) > 0 else 0
            available_materials = np.sum(position_usage <= max_usage + 2)  # 允许一定容差
            available_count *= max(1, available_materials)

        return min(available_count, 1000)  # 设置上限避免过大估算

    def set_material_paths(self, material_paths: List[str]):
        """设置素材路径，用于相似性分析"""
        self.material_paths = material_paths
        self.similarity_matrix = None  # 重置缓存

    def _calculate_material_similarity(self, path1: str, path2: str) -> float:
        """计算两个素材的相似性（基于文件名和路径特征）- 优化版本"""
        if not path1 or not path2:
            return 0.0

        # 提取文件名（不含扩展名）
        name1 = os.path.splitext(os.path.basename(path1))[0].lower()
        name2 = os.path.splitext(os.path.basename(path2))[0].lower()

        # 提取目录路径
        dir1 = os.path.dirname(path1).lower()
        dir2 = os.path.dirname(path2).lower()

        similarity = 0.0

        # 1. 目录相似性检查（权重0.3，降低权重）
        if dir1 == dir2:
            similarity += 0.3
        elif dir1 and dir2 and (dir1 in dir2 or dir2 in dir1):
            similarity += 0.15

        # 2. 文件名长前缀相似性（权重0.4）
        # 只有当共同前缀长度>=5且占较短文件名的50%以上时才认为相似
        common_prefix_len = 0
        for i in range(min(len(name1), len(name2))):
            if name1[i] == name2[i]:
                common_prefix_len += 1
            else:
                break

        min_name_len = min(len(name1), len(name2))
        if common_prefix_len >= 5 and common_prefix_len >= min_name_len * 0.5:
            similarity += 0.4

        # 3. 特殊模式检测（权重0.3）
        # 检查是否是明显的序列文件（如 video_1, video_2, video_3）
        import re

        # 移除数字后的基础名称
        base_name1 = re.sub(r'\d+', '', name1).strip('_-')
        base_name2 = re.sub(r'\d+', '', name2).strip('_-')

        # 只有当基础名称完全相同且长度>=3时才认为是序列
        if base_name1 == base_name2 and len(base_name1) >= 3:
            # 进一步检查：确保是连续序列而不是随机相同名称
            numbers1 = [int(x) for x in re.findall(r'\d+', name1) if x.isdigit()]
            numbers2 = [int(x) for x in re.findall(r'\d+', name2) if x.isdigit()]

            # 只有当数字序列相近时才认为相似（差值<=3）
            if numbers1 and numbers2:
                min_diff = min(abs(n1 - n2) for n1 in numbers1 for n2 in numbers2)
                if min_diff <= 3:
                    similarity += 0.3

        return min(1.0, similarity)

    def _get_similarity_matrix(self) -> np.ndarray:
        """获取素材相似性矩阵"""
        if self.similarity_matrix is not None:
            return self.similarity_matrix

        n = len(self.material_paths)
        if n == 0:
            return np.zeros((self.num_materials, self.num_materials))

        # 计算相似性矩阵
        similarity_matrix = np.zeros((n, n))
        for i in range(n):
            for j in range(i + 1, n):
                similarity = self._calculate_material_similarity(
                    self.material_paths[i],
                    self.material_paths[j]
                )
                similarity_matrix[i][j] = similarity
                similarity_matrix[j][i] = similarity

        # 扩展到完整大小
        full_matrix = np.zeros((self.num_materials, self.num_materials))
        full_matrix[:n, :n] = similarity_matrix

        self.similarity_matrix = full_matrix
        return self.similarity_matrix

    def _check_material_diversity(self, video_segments: List[VideoSegment]) -> bool:
        """检查视频中素材的多样性"""
        # 如果禁用相似性检测，直接通过
        if not self.enable_similarity_check:
            return True

        if not self.material_paths:
            return True  # 如果没有路径信息，跳过检查

        similarity_matrix = self._get_similarity_matrix()
        high_similarity_threshold = 0.9  # 高相似性阈值，只过滤极相似的素材

        # 检查任意两个素材的相似性
        for i, seg1 in enumerate(video_segments):
            for j, seg2 in enumerate(video_segments[i + 1:], i + 1):
                if seg1.video_index < len(self.material_paths) and seg2.video_index < len(self.material_paths):
                    similarity = similarity_matrix[seg1.video_index][seg2.video_index]
                    if similarity > high_similarity_threshold:
                        return False  # 发现高相似性素材

        return True

    def generate_optimal_videos(self, max_videos: int = 200, progress_callback=None) -> Tuple[int, List[GeneratedVideo]]:
        """使用混合策略生成最优数量的视频"""
        # 设置固定随机种子，确保每次计算结果稳定
        import random
        random.seed(42)

        self.reset()
        consecutive_failures = 0

        print("开始使用混合策略生成视频...")
        if progress_callback:
            progress_callback(0, f"开始生成视频，目标: {max_videos}个")

        while len(self.generated_videos) < max_videos:
            # 确定当前阶段
            current_phase = self.determine_current_phase()

            if current_phase != self.current_phase:
                print(f"切换到第{current_phase}阶段策略")
                self.current_phase = current_phase

            # 根据阶段选择策略
            if current_phase == 1:
                candidate_video = self.phase1_position_rotation_strategy()
            elif current_phase == 2:
                candidate_video = self.phase2_conflict_minimization_strategy()
            else:  # phase 3
                candidate_video = self.phase3_greedy_strategy()

            # 检查生成的视频
            if candidate_video and self.is_video_valid(candidate_video, max_videos):
                self.generated_videos.append(candidate_video)
                self.update_usage_matrix(candidate_video)
                consecutive_failures = 0

                if len(self.generated_videos) % 10 == 0:
                    uniformity = self.get_usage_uniformity()
                    # 打印素材使用统计
                    usage_stats = self.get_material_usage_stats()
                    used_count = len(usage_stats)
                    unused_count = self.num_materials - used_count
                    progress = len(self.generated_videos) / max_videos * 100
                    message = f"已生成 {len(self.generated_videos)}/{max_videos} 个视频，当前均匀度: {uniformity:.3f}"
                    print(message)
                    print(f"📊 素材使用: {used_count}个已用, {unused_count}个未用")
                    if progress_callback:
                        progress_callback(progress, message)
            else:
                consecutive_failures += 1

            # 如果连续失败太多次，提前结束
            if consecutive_failures > self.max_consecutive_failures:
                message = f"连续失败 {consecutive_failures} 次，停止生成"
                print(message)
                if progress_callback:
                    progress_callback(len(self.generated_videos) / max_videos * 100, message)
                break

        final_message = f"生成完成！总共生成了 {len(self.generated_videos)} 个视频"
        print(final_message)
        if progress_callback:
            progress_callback(100, final_message)
        self.print_generation_statistics()

        return len(self.generated_videos), self.generated_videos

    def generate_optimal_videos_with_history(self, max_videos: int = 200) -> Tuple[int, List[GeneratedVideo]]:
        """基于历史记录的优化算法生成"""
        print(f"🚀 开始历史感知优化生成，目标: {max_videos}个视频")

        try:
            # 导入历史感知功能
            from .video_history_database import VideoHistoryDB, CombinationSequence

            # 创建数据库连接
            db = VideoHistoryDB()
            if not db.connect():
                print("⚠️ 数据库连接失败，使用普通优化算法")
                return self.generate_optimal_videos(max_videos)

            print("✅ 历史感知数据库连接成功")

            # 使用优化算法生成
            count, generated_videos = self.generate_optimal_videos(max_videos)

            # 过滤历史重复
            filtered_videos = []
            skipped_count = 0

            for video in generated_videos:
                # 创建组合序列
                material_indices = [seg.video_index for seg in video.segments]
                positions = [seg.position for seg in video.segments]

                combination = CombinationSequence(
                    material_indices=material_indices,
                    positions=positions
                )

                # 检查是否已存在
                if db.is_combination_used(combination):
                    skipped_count += 1
                    print(f"🔍 历史检查：组合已存在，跳过 - 哈希: {combination.get_hash()[:8]}")
                    continue

                filtered_videos.append(video)

            print(f"📊 跳过历史重复: {skipped_count} 个")
            print(f"✅ 历史感知过滤后: {len(filtered_videos)} 个视频")

            db.close()
            return len(filtered_videos), filtered_videos

        except Exception as e:
            print(f"❌ 历史感知处理失败: {e}")
            # 回退到普通优化算法
            return self.generate_optimal_videos(max_videos)

    def calculate_theoretical_max(self) -> int:
        """计算理论最大生成数量（修复版）"""
        if self.num_materials == 0 or self.segments_per_video == 0:
            return 0

        # 基础理论：素材数量除以每视频片段数
        basic_max = self.num_materials // self.segments_per_video

        # 更合理的重复使用计算
        if self.num_materials >= self.segments_per_video * 3:
            # 大量素材时，可以有更多组合
            # 使用更激进的重复使用因子
            material_ratio = self.num_materials / self.segments_per_video
            if material_ratio >= 8:  # 95/11 = 8.6，属于这种情况
                reuse_factor = min(8.0, material_ratio)  # 最多8倍
            else:
                reuse_factor = min(5.0, material_ratio)  # 最多5倍

            theoretical_with_reuse = int(basic_max * reuse_factor)
        elif self.num_materials >= self.segments_per_video * 2:
            # 中等素材数量
            reuse_factor = min(4.0, self.num_materials / self.segments_per_video)
            theoretical_with_reuse = int(basic_max * reuse_factor)
        else:
            # 素材较少
            theoretical_with_reuse = basic_max

        # 调整重复率惩罚，不要太严格
        if self.num_materials >= 50:
            # 大量素材时，重复率影响较小
            duplicate_penalty = 0.85  # 只减少15%
        else:
            # 少量素材时，重复率影响较大
            duplicate_penalty = 0.75  # 减少25%

        practical_max = int(theoretical_with_reuse * duplicate_penalty)

        # 确保至少有基础数量的2倍（更合理）
        final_max = max(basic_max * 2, practical_max)

        # 限制最大值，但不要太保守
        return min(final_max, 300)

    def print_generation_statistics(self):
        """打印生成统计信息"""
        print(f"\n=== 优化算法生成统计 ===")
        print(f"总生成视频数: {len(self.generated_videos)}")
        print(f"素材数量: {self.num_materials}")
        print(f"每视频片段数: {self.segments_per_video}")
        current_threshold = self.get_dynamic_duplicate_threshold(200)
        print(f"重复率阈值: {current_threshold * 100:.1f}% (动态调整)")

        # 使用矩阵统计
        print(f"\n素材使用统计:")
        total_usage = self.usage_matrix.sum()
        print(f"总使用次数: {total_usage}")
        print(f"平均每素材使用: {total_usage / self.num_materials:.2f}次")

        # 位置使用统计
        print(f"\n各位置使用统计:")
        for pos in range(self.segments_per_video):
            pos_usage = self.usage_matrix[:, pos].sum()
            print(f"位置{pos}: {pos_usage}次")

        # 均匀度
        uniformity = self.get_usage_uniformity()
        print(f"\n使用均匀度: {uniformity:.3f}")

        # 重复率验证
        if len(self.generated_videos) > 1:
            max_duplicate_rate = 0.0
            for i in range(len(self.generated_videos)):
                for j in range(i + 1, len(self.generated_videos)):
                    rate = self.calculate_duplicate_rate(self.generated_videos[i], self.generated_videos[j])
                    max_duplicate_rate = max(max_duplicate_rate, rate)
            print(f"最大重复率: {max_duplicate_rate * 100:.1f}%")


# 测试代码
if __name__ == "__main__":
    # 测试原有算法
    print("=== 测试原有算法 ===")
    try:
        from core.subtitle_parser import SubtitleItem
        from core.video_processor import VideoInfo

        # 模拟字幕数据
        test_subtitles = [
            SubtitleItem(1, "00:00:01,000", "00:00:03,000", "测试字幕1", 1000, 3000, 2000),
            SubtitleItem(2, "00:00:04,000", "00:00:06,500", "测试字幕2", 4000, 6500, 2500),
            SubtitleItem(3, "00:00:07,000", "00:00:09,000", "测试字幕3", 7000, 9000, 2000),
        ]

        # 模拟视频数据
        test_videos = [
            VideoInfo("video1.mp4", 10.0, 1920, 1080, 30.0, 1000000),
            VideoInfo("video2.mp4", 15.0, 1920, 1080, 30.0, 1500000),
        ]

        # 测试匹配算法
        matcher = VideoMatcher()
        matcher.set_parameters(min_speed=0.8, max_speed=1.2, repeat_scale=2)

        count, results = matcher.compute_generation_count(test_subtitles, test_videos)

        print(f"原算法可生成视频数量: {count}")
        matcher.print_generation_stats(test_subtitles, test_videos, results)
    except Exception as e:
        print(f"原算法测试失败: {e}")

    # 测试新的优化算法
    print(f"\n=== 测试优化算法 ===")
    generator = OptimizedVideoGenerator(num_materials=100, segments_per_video=4)
    count, videos = generator.generate_optimal_videos(max_videos=150)

    print(f"\n优化算法理论分析:")
    print(f"100个素材，4段组合，30%重复率限制")
    print(f"实际生成: {count} 个视频")
    print(f"理论效率: {count/100:.1f}倍素材利用率")

    def phase3_greedy_strategy(self) -> Optional[GeneratedVideo]:
        """第三阶段：贪心策略"""
        max_attempts = 200

        for _ in range(max_attempts):
            segments = []
            used_materials = set()

            for position in range(self.segments_per_video):
                available_materials = [i for i in range(self.num_materials)
                                     if i not in used_materials]

                if not available_materials:
                    break

                # 简单随机选择
                selected_material = random.choice(available_materials)
                used_materials.add(selected_material)

                start_time = random.uniform(0, 5)
                duration = random.uniform(2, 3)

                segment = VideoSegment(
                    video_index=selected_material,
                    position=position,
                    start_time=start_time,
                    duration=duration
                )
                segments.append(segment)

            if len(segments) == self.segments_per_video:
                video_id = f"video_{len(self.generated_videos) + 1}"
                candidate_video = GeneratedVideo(segments=segments, video_id=video_id)

                # 直接检查是否有效
                if self.is_video_valid(candidate_video, 200):  # 使用默认最大值
                    return candidate_video

        return None

    def update_usage_matrix(self, video: GeneratedVideo):
        """更新使用矩阵"""
        for segment in video.segments:
            self.usage_matrix[segment.video_index, segment.position] += 1

    def determine_current_phase(self) -> int:
        """确定当前应该使用的策略阶段"""
        if len(self.generated_videos) == 0:
            return 1

        # 检查是否应该进入第二阶段
        uniformity = self.get_usage_uniformity()
        if uniformity < self.phase1_threshold:
            # 检查是否应该进入第三阶段
            available_combinations = self._estimate_available_combinations()
            if available_combinations < self.phase2_threshold:
                return 3
            return 2

        return 1

    def _estimate_available_combinations(self) -> int:
        """估算剩余可用组合数量"""
        # 简化估算：计算每个位置上使用次数较少的素材数量
        available_count = 1
        for position in range(self.segments_per_video):
            position_usage = self.usage_matrix[:, position]
            max_usage = position_usage.max() if len(position_usage) > 0 else 0
            available_materials = np.sum(position_usage <= max_usage + 2)  # 允许一定容差
            available_count *= max(1, available_materials)

        return min(available_count, 1000)  # 设置上限避免过大估算



