#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录对话框
Login Dialog
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QMessageBox, QApplication, QCheckBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QFont, QClipboard

try:
    from ..core.auth_manager import AuthManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.auth_manager import AuthManager


class LoginCheckThread(QThread):
    """登录检查线程"""
    
    result_ready = pyqtSignal(bool, str, str)  # 第三个参数改为字符串避免整数溢出
    
    def __init__(self, auth_manager: AuthManager, is_manual: bool = False):
        super().__init__()
        self.auth_manager = auth_manager
        self.is_manual = is_manual
    
    def run(self):
        """运行线程"""
        try:
            success, message, expire_ts = self.auth_manager.check_login(self.is_manual)
            self.result_ready.emit(success, message, str(expire_ts))  # 转换为字符串
        except Exception as e:
            self.result_ready.emit(False, f"检查登录失败: {e}", "0")


class LoginDialog(QDialog):
    """登录对话框"""
    
    login_success = pyqtSignal(str)  # 登录成功信号，传递过期时间戳（字符串形式避免整数溢出）
    
    def __init__(self, device_id: str, auth_manager: AuthManager, config, parent=None):
        super().__init__(parent)
        self.device_id = device_id
        self.auth_manager = auth_manager
        self.config = config
        self.auth_manager.set_device_info(device_id)
        
        self.check_thread = None
        self.auto_check_timer = QTimer()
        self.auto_check_timer.timeout.connect(self.auto_check_login)
        
        self.init_ui()
        self.load_saved_card_key()  # 加载保存的卡密

        # 加载卡密后再进行自动检查
        QTimer.singleShot(100, self.auto_check_login)  # 延迟100ms执行自动检查

        # 启动自动检查（每5分钟检查一次）
        self.auto_check_timer.start(5 * 60 * 1000)

    def resizeEvent(self, event):
        """重写resize事件，强制保持固定大小"""
        # 强制保持固定大小 500x520
        if event.size().width() != 500 or event.size().height() != 520:
            self.resize(500, 520)
            return
        super().resizeEvent(event)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("授权验证 - 视频混剪工具")

        # 设置窗口标志，禁用最大化按钮和大小调整
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)

        # 固定窗口大小
        self.setFixedSize(500, 520)
        self.setMinimumSize(500, 520)
        self.setMaximumSize(500, 520)
        self.setModal(True)

        # 禁用布局自动调整
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
        """)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(18)
        main_layout.setContentsMargins(35, 25, 35, 25)

        # 标题
        title_label = QLabel("视频混剪工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #1a1a1a;
                margin: 8px 0;
                padding: 5px;
                font-weight: bold;
            }
        """)
        title_label.setMinimumHeight(60)
        title_label.setMinimumWidth(300)  # 确保宽度足够
        title_label.adjustSize()
        main_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("授权验证")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Microsoft YaHei", 12))
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #333333;
                margin-bottom: 15px;
                padding: 3px;
                font-weight: normal;
            }
        """)
        subtitle_label.setMinimumHeight(40)
        subtitle_label.setMinimumWidth(150)  # 确保宽度足够
        subtitle_label.adjustSize()
        main_layout.addWidget(subtitle_label)

        # 设备ID显示
        device_label = QLabel("设备ID:")
        device_label.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        device_label.setStyleSheet("""
            QLabel {
                color: #000000;
                margin-bottom: 5px;
                padding: 2px;
                font-weight: bold;
            }
        """)
        device_label.setMinimumHeight(20)
        device_label.setMinimumWidth(80)  # 确保宽度足够
        device_label.adjustSize()
        main_layout.addWidget(device_label)

        device_layout = QHBoxLayout()
        self.device_input = QLineEdit(self.device_id)
        self.device_input.setReadOnly(True)
        self.device_input.setMinimumHeight(40)
        self.device_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                font-family: 'Consolas', monospace;
                background-color: #f8f9fa;
                color: #5a6c7d;
            }
        """)

        copy_btn = QPushButton("复制")
        copy_btn.setFixedSize(60, 40)
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #ecf0f1;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                color: #5a6c7d;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #d5dbdb;
            }
        """)
        copy_btn.clicked.connect(self.copy_device_id)

        device_layout.addWidget(self.device_input)
        device_layout.addWidget(copy_btn)
        main_layout.addLayout(device_layout)

        # 卡密输入
        card_label = QLabel("卡密:")
        card_label.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        card_label.setStyleSheet("""
            QLabel {
                color: #000000;
                margin: 15px 0 5px 0;
                padding: 2px;
                font-weight: bold;
            }
        """)
        card_label.setMinimumHeight(40)
        card_label.setMinimumWidth(60)  # 确保宽度足够
        card_label.adjustSize()
        main_layout.addWidget(card_label)

        self.card_input = QLineEdit()
        self.card_input.setPlaceholderText("请输入您的卡密")
        self.card_input.setMinimumHeight(40)
        self.card_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                background-color: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QLineEdit::placeholder {
                color: #bdc3c7;
            }
        """)
        main_layout.addWidget(self.card_input)

        # 记住卡密复选框（隐藏，默认选中）
        self.remember_checkbox = QCheckBox("记住卡密")
        self.remember_checkbox.setChecked(True)  # 默认选中
        self.remember_checkbox.setVisible(False)  # 隐藏控件

        # 添加间距
       # main_layout.addSpacing(25)

        # 验证按钮
        self.check_btn = QPushButton("验证授权")
        self.check_btn.setMinimumHeight(42)
        self.check_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1f618d;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.check_btn.clicked.connect(self.manual_check_login)
        main_layout.addWidget(self.check_btn)

        # 状态显示
        self.status_label = QLabel("授权验证失败：请先输入卡密")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        self.status_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                margin: 12px 0;
                padding: 8px 12px;
                background-color: white;
                border-radius: 6px;
                border: 1px solid #ecf0f1;
                min-height: 20px;
            }
        """)
        self.status_label.setWordWrap(True)  # 允许文字换行
        main_layout.addWidget(self.status_label)

        # 底部客服信息
        service_label = QLabel("有任何问题，请联系裕页")
        service_label.setAlignment(Qt.AlignCenter)
        service_label.setFont(QFont("Microsoft YaHei", 10))
        service_label.setStyleSheet("color: #95a5a6; margin-top: 8px;")
        service_label.setMinimumHeight(25)
        main_layout.addWidget(service_label)

    def save_card_key(self, card_key: str):
        """保存卡密到配置文件"""
        try:
            self.config.save_card_key(card_key)
        except Exception as e:
            print(f"保存卡密失败: {e}")

    def load_saved_card_key(self):
        """加载保存的卡密"""
        try:
            card_key = self.config.load_saved_card_key()
            if card_key:
                # 设置到输入框
                self.card_input.setText(card_key)
                self.remember_checkbox.setChecked(True)

                # 如果有保存的卡密，自动尝试验证
                self.auth_manager.set_card_key(card_key)

        except Exception as e:
            print(f"加载卡密失败: {e}")
    
    def copy_device_id(self):
        """复制设备ID"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.device_id)
        
        # 显示提示
        self.status_label.setText("设备ID已复制到剪贴板")
        QTimer.singleShot(2000, lambda: self.status_label.setText(""))
    
    def auto_check_login(self):
        """自动检查登录状态"""
        if self.check_thread and self.check_thread.isRunning():
            return
        
        self.status_label.setText("正在检查授权状态...")
        self.check_btn.setEnabled(False)
        
        self.check_thread = LoginCheckThread(self.auth_manager, False)
        self.check_thread.result_ready.connect(self.on_login_result)
        self.check_thread.start()
    
    def manual_check_login(self):
        """手动检查登录状态"""
        if self.check_thread and self.check_thread.isRunning():
            return

        # 获取用户输入的卡密
        card_key = self.card_input.text().strip()
        if not card_key:
            self.status_label.setText("请先输入卡密！")
            return

        # 如果选择了记住卡密，则保存
        if self.remember_checkbox.isChecked():
            self.save_card_key(card_key)

        # 设置卡密到授权管理器
        self.auth_manager.set_card_key(card_key)

        self.status_label.setText("正在验证授权状态...")
        self.check_btn.setEnabled(False)

        self.check_thread = LoginCheckThread(self.auth_manager, True)
        self.check_thread.result_ready.connect(self.on_login_result)
        self.check_thread.start()



    def on_login_result(self, success: bool, message: str, expire_ts_str: str):
        """处理登录结果"""
        self.check_btn.setEnabled(True)

        if success:
            expire_ts = int(expire_ts_str)  # 从字符串转换为整数
            # 确保授权管理器的过期时间戳被正确设置
            self.auth_manager.expire_timestamp = expire_ts
            self.status_label.setText(f"授权验证成功！到期时间：{self.auth_manager.get_expire_time_string()}")

            # 延迟关闭对话框
            QTimer.singleShot(1000, lambda: self.accept_login(expire_ts))
        else:
            self.status_label.setText(f"授权验证失败：{message}")

            # 如果是手动检查，显示错误消息
            if hasattr(self.check_thread, 'is_manual') and self.check_thread.is_manual:
                QMessageBox.warning(self, "授权验证失败", message)
    
    def accept_login(self, expire_ts: int):
        """接受登录"""
        self.login_success.emit(str(expire_ts))  # 转换为字符串传递
        self.accept()
    
    def closeEvent(self, event):
        """关闭事件"""
        # 停止自动检查定时器
        if self.auto_check_timer.isActive():
            self.auto_check_timer.stop()
        
        # 等待线程结束
        if self.check_thread and self.check_thread.isRunning():
            self.check_thread.quit()
            self.check_thread.wait()
        
        event.accept()
    
    def reject(self):
        """拒绝对话框（用户点击关闭按钮）"""
        reply = QMessageBox.question(
            self, 
            "确认退出", 
            "未完成授权验证，确定要退出程序吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            super().reject()


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    from ..utils.device_id import DeviceIDGenerator
    from ..core.auth_manager import AuthManager
    
    app = QApplication(sys.argv)
    
    # 生成设备ID
    device_id = DeviceIDGenerator.generate_device_id()
    
    # 创建授权管理器
    auth_manager = AuthManager()
    
    # 创建登录对话框
    dialog = LoginDialog(device_id, auth_manager)
    
    if dialog.exec_() == QDialog.Accepted:
        print("登录成功")
    else:
        print("登录取消")
    
    sys.exit()
