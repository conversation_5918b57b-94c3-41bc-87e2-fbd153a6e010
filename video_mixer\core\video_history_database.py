#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成历史数据库 - 专门用于历史感知去重
记录生成视频的MD5和组合序列，用于避免重复生成
"""

import mysql.connector
import hashlib
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass

@dataclass
class VideoRecord:
    """生成视频记录"""
    id: int
    batch_id: str
    video_path: str  # 生成视频的路径
    combination_sequence: str  # 组合序列的JSON字符串
    combination_hash: str  # 组合序列的哈希值
    material_md5s: List[str]  # 使用的素材MD5列表
    quality_score: float
    created_at: datetime
    file_size: Optional[int] = None
    duration_seconds: Optional[float] = None

@dataclass
class CombinationSequence:
    """组合序列"""
    material_indices: List[int]  # 素材索引
    positions: List[float]  # 位置信息
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps({
            'material_indices': self.material_indices,
            'positions': self.positions
        })
    
    @classmethod
    def from_json(cls, json_str: str) -> 'CombinationSequence':
        """从JSON字符串创建"""
        data = json.loads(json_str)
        return cls(
            material_indices=data['material_indices'],
            positions=data['positions']
        )
    
    def get_hash(self) -> str:
        """获取组合序列的哈希值"""
        content = f"{self.material_indices}_{self.positions}"
        return hashlib.md5(content.encode()).hexdigest()

class VideoHistoryDB:
    """视频生成历史数据库"""
    
    def __init__(self, host='*************', user='root', password='root', database='video_mixed'):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4'
            )
            self.cursor = self.connection.cursor()
            self._create_tables()
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def _create_tables(self):
        """创建数据表（如果不存在）"""
        try:
            # 检查表是否已存在
            self.cursor.execute("SHOW TABLES LIKE 'generated_videos'")
            if self.cursor.fetchone():
                print("✅ generated_videos表已存在，检查表结构...")
                self._check_and_update_table_structure()
                return

            # 生成视频记录表（与新表结构一致）
            create_videos_table = """
            CREATE TABLE IF NOT EXISTS generated_videos (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_id VARCHAR(100) NOT NULL,
                video_path VARCHAR(500) NOT NULL,
                combination_sequence TEXT NOT NULL,
                combination_hash VARCHAR(32) NOT NULL,
                material_md5s JSON NOT NULL,
                quality_score DECIMAL(5,3) DEFAULT 0.000,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_size BIGINT,
                duration_seconds DECIMAL(10,3),
                INDEX idx_batch_id (batch_id),
                INDEX idx_combination_hash (combination_hash),
                INDEX idx_created_at (created_at),
                INDEX idx_quality_score (quality_score)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # 素材信息表
            create_materials_table = """
            CREATE TABLE IF NOT EXISTS materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                md5_hash VARCHAR(32) NOT NULL UNIQUE,
                file_name VARCHAR(255) NOT NULL,
                file_path TEXT NOT NULL,
                file_size BIGINT NOT NULL DEFAULT 0,
                duration FLOAT NOT NULL DEFAULT 0.0,
                first_seen TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_md5_hash (md5_hash)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """

            # 批次信息表
            create_batches_table = """
            CREATE TABLE IF NOT EXISTS generation_batches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_id VARCHAR(64) NOT NULL UNIQUE,
                total_videos INT NOT NULL,
                successful_videos INT NOT NULL,
                algorithm_type VARCHAR(32) NOT NULL,
                generation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_batch_id (batch_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """

            self.cursor.execute(create_videos_table)
            self.cursor.execute(create_materials_table)
            self.cursor.execute(create_batches_table)
            self.connection.commit()
            print("✅ 数据表创建成功")

        except Exception as e:
            print(f"⚠️ 创建表时出现问题: {e}")
            # 不抛出异常，继续使用现有表

    def _check_and_update_table_structure(self):
        """检查并更新表结构"""
        try:
            # 检查generation_batches表是否有successful_videos字段
            self.cursor.execute("SHOW COLUMNS FROM generation_batches LIKE 'successful_videos'")
            if not self.cursor.fetchone():
                print("🔧 添加缺失的successful_videos字段...")
                self.cursor.execute("ALTER TABLE generation_batches ADD COLUMN successful_videos INT NOT NULL DEFAULT 0")
                self.connection.commit()
                print("✅ 成功添加successful_videos字段")

            # 检查其他可能缺失的字段
            self.cursor.execute("SHOW COLUMNS FROM generation_batches LIKE 'algorithm_type'")
            if not self.cursor.fetchone():
                print("🔧 添加缺失的algorithm_type字段...")
                self.cursor.execute("ALTER TABLE generation_batches ADD COLUMN algorithm_type VARCHAR(32) NOT NULL DEFAULT 'unknown'")
                self.connection.commit()
                print("✅ 成功添加algorithm_type字段")

        except Exception as e:
            print(f"⚠️ 更新表结构时出现问题: {e}")
            # 不抛出异常，继续使用现有表
    

    
    def record_generated_video(self, video_path: str, combination_sequence: CombinationSequence,
                             material_md5s: List[str], batch_id: str, quality_score: float = 0.0) -> bool:
        """记录生成的视频"""
        try:
            # 检查组合是否已存在
            if self.is_combination_used(combination_sequence):
                print(f"组合已存在: {combination_sequence.get_hash()[:8]}")
                return True

            # 插入记录
            insert_sql = """
            INSERT INTO generated_videos
            (batch_id, video_path, combination_sequence, combination_hash, material_md5s, quality_score)
            VALUES (%s, %s, %s, %s, %s, %s)
            """

            values = (
                batch_id,
                video_path,
                combination_sequence.to_json(),
                combination_sequence.get_hash(),
                json.dumps(material_md5s),
                quality_score
            )
            
            self.cursor.execute(insert_sql, values)
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"记录生成视频失败: {e}")
            return False
    
    def is_combination_used(self, combination_sequence: CombinationSequence) -> bool:
        """检查组合序列是否已被使用"""
        try:
            combination_hash = combination_sequence.get_hash()
            query = "SELECT COUNT(*) FROM generated_videos WHERE combination_hash = %s"
            self.cursor.execute(query, (combination_hash,))
            count = self.cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查组合序列失败: {e}")
            return False
    

    
    def get_used_combinations(self) -> Set[str]:
        """获取所有已使用的组合序列哈希"""
        try:
            query = "SELECT combination_hash FROM generated_videos"
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            return {row[0] for row in results}
        except Exception as e:
            print(f"获取已使用组合失败: {e}")
            return set()
    
    def record_material(self, file_path: str) -> Optional[str]:
        """记录素材信息并返回MD5"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return None

            # 计算MD5
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            md5_hash = hash_md5.hexdigest()

            # 获取文件信息
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # 插入或更新记录
            insert_sql = """
            INSERT INTO materials (md5_hash, file_name, file_path, file_size, duration, first_seen, last_seen)
            VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            file_name = VALUES(file_name),
            file_path = VALUES(file_path),
            file_size = VALUES(file_size),
            last_seen = NOW()
            """

            # 这里简化处理，duration设为0，实际使用时可以通过ffprobe获取
            values = (md5_hash, file_name, file_path, file_size, 0.0)
            self.cursor.execute(insert_sql, values)
            self.connection.commit()

            return md5_hash

        except Exception as e:
            print(f"记录素材失败: {e}")
            return None
    
    def create_batch_record(self, batch_id: str, total_videos: int, 
                          successful_videos: int, algorithm_type: str) -> bool:
        """创建批次记录"""
        try:
            insert_sql = """
            INSERT INTO generation_batches (batch_id, total_videos, successful_videos, algorithm_type)
            VALUES (%s, %s, %s, %s)
            """
            values = (batch_id, total_videos, successful_videos, algorithm_type)
            self.cursor.execute(insert_sql, values)
            self.connection.commit()
            return True
        except Exception as e:
            print(f"创建批次记录失败: {e}")
            return False
    
    def get_generation_stats(self) -> Dict:
        """获取生成统计信息"""
        try:
            stats = {}
            
            # 总视频数
            self.cursor.execute("SELECT COUNT(*) FROM generated_videos")
            stats['total_videos'] = self.cursor.fetchone()[0]
            
            # 总批次数
            self.cursor.execute("SELECT COUNT(*) FROM generation_batches")
            stats['total_batches'] = self.cursor.fetchone()[0]
            
            # 总素材数
            self.cursor.execute("SELECT COUNT(*) FROM materials")
            stats['total_materials'] = self.cursor.fetchone()[0]
            
            # 最近生成时间
            self.cursor.execute("SELECT MAX(generation_time) FROM generated_videos")
            result = self.cursor.fetchone()[0]
            stats['last_generation'] = result.strftime('%Y-%m-%d %H:%M:%S') if result else '无'
            
            return stats
            
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
