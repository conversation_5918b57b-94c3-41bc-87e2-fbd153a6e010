# 视频混剪工具-逛逛 (Python版本)

基于PyQt5和FFmpeg的视频自动化混剪工具，移植自原Electron版本。

## 功能特性

- **SRT字幕解析**：支持标准SRT字幕文件解析
- **视频素材处理**：批量处理MP4视频文件，自动获取视频信息
- **音频集成**：支持MP3等音频格式
- **智能匹配算法**：根据字幕时长自动匹配最优视频片段
- **参数化控制**：可调节倍速、缩放比例、重复阈值等参数
- **字幕样式设置**：支持字体、大小、颜色、描边等样式设置
- **授权管理**：设备ID验证和在线授权检查
- **批量生成**：一键生成多个不同版本的视频

## 系统要求

- Python 3.7+
- FFmpeg (已添加到系统环境变量)
- PyQt5
- 其他依赖见 requirements.txt

## 安装步骤

1. **克隆项目**
```bash
git clone <repository_url>
cd video_mixer
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **确保FFmpeg可用**
```bash
ffmpeg -version
```

4. **运行程序**
```bash
python main.py
# 或者
python run.py
```

## 使用说明

### 1. 授权验证
- 程序启动时会显示登录对话框
- 点击授权链接进行充值续费
- 设备ID基于MAC地址和硬盘序列号生成

### 2. 文件准备
- **SRT字幕文件**：标准格式的字幕文件
- **音频文件**：MP3、WAV等格式的背景音乐
- **视频素材**：MP4格式的视频文件（建议分辨率大于输出分辨率）
- **导出文件夹**：选择视频输出目录

### 3. 参数设置
- **倍速范围**：设置视频播放速度的最小值和最大值
- **缩放比例**：设置视频放大的最大倍数
- **视频尺寸**：选择输出视频的分辨率（1080:1440 或 1080:1920）
- **重复阈值**：控制素材重复使用的频率

### 4. 字幕样式
- **字体选择**：从系统字体中选择
- **字幕大小**：设置字体大小
- **字幕位置**：设置字幕距离底部的距离
- **颜色设置**：设置字幕颜色和描边颜色
- **描边大小**：设置字幕描边粗细

### 5. 生成视频
1. 点击"计算视频生成数"按钮
2. 系统会分析素材并计算可生成的视频数量
3. 点击"生成视频"开始批量生成
4. 生成过程中可以查看进度和日志信息

## 项目结构

```
video_mixer/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖列表
├── README.md              # 说明文档
├── gui/                   # GUI界面模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口
│   ├── login_dialog.py    # 登录对话框
│   └── video_generation_thread.py  # 视频生成线程
├── core/                  # 核心功能模块
│   ├── __init__.py
│   ├── subtitle_parser.py # 字幕解析
│   ├── video_processor.py # 视频处理
│   ├── algorithm.py       # 匹配算法
│   └── auth_manager.py    # 授权管理
└── utils/                 # 工具模块
    ├── __init__.py
    ├── config.py          # 配置管理
    └── device_id.py       # 设备ID生成
```

## 核心算法

### 智能匹配算法
1. **参数计算**：根据倍速范围和重复阈值计算所有可能的参数组合
2. **素材筛选**：验证视频素材的分辨率和时长是否满足要求
3. **随机匹配**：使用随机化算法为每个字幕片段匹配最优的视频片段
4. **多次尝试**：进行多轮匹配尝试，选择最优方案

### 视频处理流程
1. **片段处理**：根据匹配结果处理每个视频片段（裁剪、缩放、调速）
2. **片段合并**：将处理后的片段按顺序合并
3. **音频混合**：添加背景音乐
4. **字幕烧录**：将字幕烧录到视频中
5. **输出优化**：使用H.264编码优化输出质量

## 注意事项

1. **素材要求**：
   - 视频素材分辨率应大于输出分辨率
   - 视频时长应足够支持字幕时长需求
   - 音频时长应覆盖所有字幕时长

2. **性能优化**：
   - 建议使用SSD存储提高处理速度
   - 处理大量素材时建议关闭其他占用CPU的程序
   - 根据CPU性能调整并发处理数量

3. **素材重复阈值设置**：
   - 素材数量100以下设置为1
   - 素材数量200-300设置为2
   - 素材数量300以上设置为3

## 故障排除

### 常见问题

1. **FFmpeg不可用**
   - 确保FFmpeg已安装并添加到环境变量
   - 在命令行中测试 `ffmpeg -version`

2. **字幕解析失败**
   - 检查SRT文件编码格式（支持UTF-8、GBK等）
   - 确认字幕文件格式正确

3. **视频处理失败**
   - 检查视频文件是否损坏
   - 确认视频格式为MP4
   - 检查磁盘空间是否充足

4. **授权验证失败**
   - 检查网络连接
   - 确认设备ID正确
   - 联系客服处理授权问题

## 技术支持

- 客服微信：dun667788dun
- 技术问题请提供详细的错误日志和系统信息

## 版本历史

- v2.0.3: Python版本初始发布
  - 完整移植原Electron版本功能
  - 优化性能和用户体验
  - 增强错误处理和日志记录

## 许可证

本项目仅供学习和研究使用。
