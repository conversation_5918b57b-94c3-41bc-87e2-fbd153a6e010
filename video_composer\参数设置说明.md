# 视频智能合成工具 - 参数设置说明

## 📊 参数详解

### 1. 场景检测阈值 (Scene Detection Threshold)
- **范围**: 0.01 - 1.0
- **默认值**: 0.1
- **说明**: 控制场景变化的敏感度
  - **数值越小**: 越敏感，检测到更多场景变化点，生成更多短片段
  - **数值越大**: 越不敏感，检测到较少场景变化点，生成较少长片段
- **实际FFmpeg阈值**: 界面值 ÷ 10
- **建议**:
  - 动作片/快节奏视频: 0.05-0.08
  - 普通视频: 0.1-0.15
  - 静态/慢节奏视频: 0.2-0.3

### 2. 最短片段时长
- **范围**: 0.5 - 10.0 秒
- **默认值**: 1.0 秒
- **说明**: 小于此时长的片段会被跳过
- **作用**: 避免生成过短的无意义片段
- **建议**:
  - 快节奏视频: 0.5-1.0 秒
  - 普通视频: 1.0-2.0 秒
  - 慢节奏视频: 2.0-3.0 秒

### 3. 固定分割间隔
- **范围**: 5.0 - 60.0 秒
- **默认值**: 10.0 秒
- **说明**: 当场景检测失败时的备用分割方案
- **触发条件**: 检测不到场景变化时自动启用
- **建议**:
  - 短视频: 5-10 秒
  - 中等视频: 10-20 秒
  - 长视频: 20-30 秒

### 4. 目标时长设置
- **模式1**: 使用最短视频时长（默认）
  - 自动选择3个视频中最短的时长作为目标
  - 确保所有视频都有足够素材
- **模式2**: 自定义时长
  - **范围**: 10.0 - 3600.0 秒
  - **默认值**: 60.0 秒
  - 可以设置任意目标时长

## 🎯 使用建议

### 场景类型推荐设置

#### 🎬 电影/电视剧
```
场景检测阈值: 0.12
最短片段时长: 2.0 秒
固定分割间隔: 15.0 秒
目标时长: 使用最短视频时长
```

#### 🎮 游戏视频
```
场景检测阈值: 0.08
最短片段时长: 1.0 秒
固定分割间隔: 8.0 秒
目标时长: 自定义 120 秒
```

#### 📺 综艺节目
```
场景检测阈值: 0.15
最短片段时长: 1.5 秒
固定分割间隔: 12.0 秒
目标时长: 使用最短视频时长
```

#### 🎵 音乐MV
```
场景检测阈值: 0.06
最短片段时长: 0.8 秒
固定分割间隔: 6.0 秒
目标时长: 自定义（歌曲时长）
```

## ⚙️ 高级技巧

### 1. 调试场景检测
- 先用默认参数测试
- 查看生成的片段数量和时长
- 根据结果调整阈值:
  - 片段太多太短 → 增大阈值
  - 片段太少太长 → 减小阈值

### 2. 优化合成效果
- **多样性**: 降低阈值，增加片段多样性
- **连贯性**: 提高阈值，保持场景连贯性
- **节奏感**: 调整最短片段时长控制节奏

### 3. 处理特殊情况
- **静态视频**: 提高阈值，增大固定间隔
- **快切视频**: 降低阈值，减小最短时长
- **长视频**: 使用自定义时长，避免过长

## 🔧 故障排除

### 问题1: 生成片段太多
**解决**: 增大场景检测阈值 (0.1 → 0.15)

### 问题2: 生成片段太少
**解决**: 减小场景检测阈值 (0.1 → 0.05)

### 问题3: 片段时长不合适
**解决**: 调整最短片段时长设置

### 问题4: 场景检测失效
**解决**: 检查固定分割间隔设置，确保备用方案合理
