#!/usr/bin/env python3
"""
测试字幕文件处理
"""
import sys
import os
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_subtitle_processing():
    """测试字幕文件处理"""
    print("测试字幕文件处理...")
    
    try:
        # 创建测试字幕文件
        test_subtitle_content = """1
00:00:00,000 --> 00:00:05,000
这是测试字幕

2
00:00:05,000 --> 00:00:10,000
第二行字幕
"""
        
        # 创建临时字幕文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write(test_subtitle_content)
            test_subtitle_path = f.name
        
        print(f"创建测试字幕文件: {test_subtitle_path}")
        
        # 测试字幕文件读取
        if os.path.exists(test_subtitle_path):
            print("✓ 字幕文件创建成功")
            
            # 测试读取
            with open(test_subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✓ 字幕内容读取成功，长度: {len(content)} 字符")
            
            # 测试路径处理
            abs_path = os.path.abspath(test_subtitle_path)
            normalized_path = abs_path.replace('\\', '/')
            print(f"✓ 路径处理: {normalized_path}")
            
        else:
            print("✗ 字幕文件创建失败")
            return False
        
        # 清理测试文件
        os.unlink(test_subtitle_path)
        print("✓ 测试文件清理完成")
        
        print("✓ 字幕文件处理测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_subtitle_processing()
    sys.exit(0 if success else 1)
