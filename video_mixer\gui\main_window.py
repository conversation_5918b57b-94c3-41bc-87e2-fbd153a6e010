#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI界面
Main Window GUI Interface
"""

import os
import sys
from typing import List, Optional
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
    QComboBox, QColorDialog, QFileDialog, QProgressBar,
    QTextEdit, QGroupBox, QMessageBox, QApplication, QCheckBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette, QFontDatabase

class CalculationThread(QThread):
    """计算线程，避免界面卡顿"""
    calculation_finished = pyqtSignal(int, str)  # 计算完成信号：(数量, 算法类型)
    calculation_error = pyqtSignal(str)  # 计算错误信号
    calculation_progress = pyqtSignal(float, str)  # 计算进度信号：(进度百分比, 状态消息)

    def __init__(self, algorithm_type, videos, subtitles, audio_path, main_window):
        super().__init__()
        self.algorithm_type = algorithm_type
        self.videos = videos
        self.subtitles = subtitles
        self.audio_path = audio_path
        self.main_window = main_window

    def run(self):
        """在后台线程中执行计算"""
        try:
            # 设置固定随机种子，确保每次计算结果一致
            import random
            import numpy as np
            random.seed(42)
            np.random.seed(42)

            if self.algorithm_type == "traditional":
                count = self._calculate_traditional()
            elif self.algorithm_type in ["optimized", "original_enhanced", "test_algorithm"]:
                count = self._calculate_optimized()
            else:
                count = 0

            self.calculation_finished.emit(count, self.algorithm_type)
        except Exception as e:
            self.calculation_error.emit(str(e))

    def _calculate_traditional(self):
        """传统算法计算"""
        # 获取主窗口的参数
        size_text = self.main_window.video_size_combo.currentText()
        width, height = map(int, size_text.split(":"))

        # 设置匹配参数
        self.main_window.video_matcher.set_parameters(
            min_speed=self.main_window.min_speed_spin.value(),
            max_speed=self.main_window.max_speed_spin.value(),
            max_scale=self.main_window.max_scale_spin.value(),
            repeat_scale=self.main_window.repeat_scale_spin.value(),
            output_width=width,
            output_height=height
        )

        # 计算生成数量
        count, results = self.main_window.video_matcher.compute_generation_count(self.subtitles, self.videos)
        return count



    def _calculate_optimized(self):
        """优化算法计算（线程版本）"""
        # 使用用户设置的目标数量，不再有理论限制
        max_videos = self.main_window.target_count_spin.value()
        print(f"🎯 用户设置目标: {max_videos}个视频")

        # 固定使用质量优先模式
        optimization_mode = "quality"

        # 根据算法类型选择生成器
        if self.algorithm_type == "test_algorithm":
            from core.test_algorithm import TestVideoGenerator
            current_generator = TestVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode,
                debug_mode=False
            )
        elif self.algorithm_type == "original_enhanced":
            from core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator
            current_generator = OriginalEnhancedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode
            )

        else:  # optimized
            from core.algorithm import OptimizedVideoGenerator
            current_generator = OptimizedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode
            )

        # 设置数据库管理器
        self._setup_database_for_generator(current_generator)

        try:
            # 根据算法类型调用相应的生成方法
            if self.algorithm_type == "test_algorithm":
                # 设置素材
                current_generator.set_materials(self.videos)
                count, generated_videos = current_generator.generate_optimal_videos_test(max_videos)
            elif self.algorithm_type == "original_enhanced":
                count, generated_videos = current_generator.generate_optimal_videos_original_enhanced(max_videos)

            else:  # optimized
                # 为优化算法添加进度回调
                def progress_callback(progress, message):
                    self.calculation_progress.emit(progress, message)

                count, generated_videos = current_generator.generate_optimal_videos(max_videos, progress_callback)

            return count

        except Exception as e:
            print(f"算法计算失败: {str(e)}")
            print(f"算法类型: {self.algorithm_type}")
            print(f"素材数量: {len(self.videos) if hasattr(self, 'videos') else 'N/A'}")
            print(f"字幕数量: {len(self.subtitles) if hasattr(self, 'subtitles') else 'N/A'}")
            import traceback
            traceback.print_exc()
            # 返回0表示失败，这样GUI就会显示错误信息
            return 0

    def _setup_database_for_generator(self, generator):
        """为生成器设置数据库管理器（线程版本）"""
        try:
            from core.database import VideoGenerationDB
            import hashlib

            db_manager = VideoGenerationDB()
            material_md5s = []

            for video in self.videos:
                try:
                    with open(video.path, 'rb') as f:
                        content = f.read(1024 * 1024)  # 读取1MB
                        md5_hash = hashlib.md5(content).hexdigest()
                        material_md5s.append(md5_hash)
                except:
                    material_md5s.append(hashlib.md5(video.path.encode()).hexdigest())

            generator.set_database_manager(db_manager, material_md5s)
        except Exception as e:
            # 数据库连接失败时不影响计算
            pass

try:
    from ..core.subtitle_parser import SubtitleParser, SubtitleItem
    from ..core.video_processor import VideoProcessor, VideoInfo
    from ..core.algorithm import VideoMatcher, OptimizedVideoGenerator
    from ..core.history_aware_generator import HistoryAwareVideoGenerator

    from ..core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator
    from ..core.test_algorithm import TestVideoGenerator
    from ..core.auth_manager import AuthManager
    from ..utils.config import Config
    from ..utils.device_id import DeviceIDGenerator
    from .login_dialog import LoginDialog
    from .video_generation_thread import VideoGenerationThread
    from .history_stats_widget import HistoryStatsWidget
except ImportError:
    # 绝对导入
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.subtitle_parser import SubtitleParser, SubtitleItem
    from core.video_processor import VideoProcessor, VideoInfo
    from core.algorithm import VideoMatcher, OptimizedVideoGenerator
    from core.history_aware_generator import HistoryAwareVideoGenerator

    from core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator
    from core.test_algorithm import TestVideoGenerator
    from core.auth_manager import AuthManager
    from utils.config import Config
    from utils.device_id import DeviceIDGenerator
    from gui.login_dialog import LoginDialog
    from gui.video_generation_thread import VideoGenerationThread
    from gui.history_stats_widget import HistoryStatsWidget


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.subtitle_parser = SubtitleParser()
        self.video_processor = VideoProcessor()
        self.video_matcher = VideoMatcher()
        self.optimized_generator = OptimizedVideoGenerator(optimization_mode="balanced")
        self.history_aware_generator = None  # 延迟初始化
        self.enhanced_generator = None  # 延迟初始化
        self.original_enhanced_generator = None  # 延迟初始化
        self.test_generator = None  # 延迟初始化

        # 数据存储
        self.subtitles: List[SubtitleItem] = []
        self.videos: List[VideoInfo] = []
        self.subtitle_path: str = ""
        self.audio_path: str = ""
        self.audio_duration: float = 0.0
        self.export_path: str = ""
        self.generation_count: int = 0
        self.match_results = []

        # 设备ID和授权管理
        self.device_id = DeviceIDGenerator.generate_device_id()
        self.auth_manager = AuthManager()
        self.auth_manager.set_device_info(self.device_id)
        self.is_logged_in = False
        self.expire_timestamp = 0

        # 视频生成线程
        self.generation_thread: Optional[VideoGenerationThread] = None

        # 先初始化UI，确保log_text等组件已创建
        self.init_ui()
        self.load_settings()

        # 然后检查登录状态
        try:
            if not self.check_and_show_login():
                # 用户取消登录，抛出异常阻止窗口创建
                raise RuntimeError("用户取消登录")
        except RuntimeError:
            # 重新抛出用户取消异常
            raise
        except Exception as e:
            print(f"登录检查失败: {e}")
            # 如果登录检查失败，继续初始化但禁用功能
            self.is_logged_in = False

    def check_and_show_login(self) -> bool:
        """检查并显示登录对话框"""
        try:
            # 创建登录对话框
            login_dialog = LoginDialog(self.device_id, self.auth_manager, self.config, self)

            # 连接登录成功信号
            login_dialog.login_success.connect(self.on_login_success)

            # 显示对话框
            result = login_dialog.exec_()

            if result == LoginDialog.Accepted:
                return True
            else:
                # 用户取消登录，返回False
                return False

        except Exception as e:
            print(f"显示登录对话框失败: {e}")
            QMessageBox.critical(None, "错误", f"初始化失败: {e}")
            return False

    def on_login_success(self, expire_timestamp_str: str):
        """登录成功处理"""
        expire_timestamp = int(expire_timestamp_str)  # 从字符串转换回整数
        print(f"主窗口收到的过期时间戳: {expire_timestamp}")
        self.is_logged_in = True
        self.expire_timestamp = expire_timestamp
        # 同时设置auth_manager的过期时间，确保is_expired()返回正确结果
        self.auth_manager.expire_timestamp = expire_timestamp
        self.auth_manager.is_logged_in = True
        print(f"设置到auth_manager后的时间戳: {self.auth_manager.expire_timestamp}")
        self.log(f"授权验证成功，到期时间: {self.auth_manager.get_expire_time_string()}")

    def _get_smart_target_count(self):
        """根据素材数量智能计算目标数量"""
        if not hasattr(self, 'videos') or not self.videos:
            return 500  # 默认值

        material_count = len(self.videos)

        # 根据素材数量智能设置目标
        if material_count < 50:
            return 200  # 素材少，目标也少
        elif material_count < 100:
            return 500  # 中等素材，中等目标
        elif material_count < 200:
            return 800  # 较多素材，较高目标
        else:
            return 1000  # 大量素材，高目标

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("视频混剪工具-裕页")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建各个功能区域
        self.create_file_selection_area(main_layout)
        self.create_parameter_area(main_layout)
        self.create_subtitle_style_area(main_layout)
        self.create_control_area(main_layout)
        self.create_log_area(main_layout)
        
        # 设置样式
        self.set_style()
    
    def create_file_selection_area(self, parent_layout):
        """创建文件选择区域"""
        group_box = QGroupBox("文件选择")
        layout = QGridLayout(group_box)
        
        # SRT字幕文件
        self.subtitle_btn = QPushButton("选择SRT字幕")
        self.subtitle_label = QLabel("未选择字幕文件")
        self.subtitle_count_label = QLabel("字幕片段数：0条")
        
        layout.addWidget(self.subtitle_btn, 0, 0)
        layout.addWidget(self.subtitle_label, 0, 1)
        layout.addWidget(self.subtitle_count_label, 0, 2)
        
        # 音频文件
        self.audio_btn = QPushButton("选择音频素材")
        self.audio_label = QLabel("未选择音频文件")
        self.audio_duration_label = QLabel("音频时长：0秒")
        
        layout.addWidget(self.audio_btn, 1, 0)
        layout.addWidget(self.audio_label, 1, 1)
        layout.addWidget(self.audio_duration_label, 1, 2)
        
        # 视频文件
        self.video_btn = QPushButton("选择视频素材")
        self.video_label = QLabel("未选择视频文件")
        self.video_count_label = QLabel("视频素材数：0条")
        
        layout.addWidget(self.video_btn, 2, 0)
        layout.addWidget(self.video_label, 2, 1)
        layout.addWidget(self.video_count_label, 2, 2)
        
        # 导出文件夹
        self.export_btn = QPushButton("选择导出文件夹")
        self.export_label = QLabel("未选择导出文件夹")
        
        layout.addWidget(self.export_btn, 3, 0)
        layout.addWidget(self.export_label, 3, 1, 1, 2)
        
        # 连接信号
        self.subtitle_btn.clicked.connect(self.select_subtitle_file)
        self.audio_btn.clicked.connect(self.select_audio_file)
        self.video_btn.clicked.connect(self.select_video_files)
        self.export_btn.clicked.connect(self.select_export_folder)
        
        parent_layout.addWidget(group_box)
    
    def create_parameter_area(self, parent_layout):
        """创建参数设置区域"""
        group_box = QGroupBox("参数设置")
        layout = QGridLayout(group_box)
        
        # 倍速设置
        layout.addWidget(QLabel("最低倍速（倍）:"), 0, 0)
        self.min_speed_spin = QDoubleSpinBox()
        self.min_speed_spin.setRange(0.1, 5.0)
        self.min_speed_spin.setSingleStep(0.1)
        self.min_speed_spin.setValue(0.9)
        layout.addWidget(self.min_speed_spin, 0, 1)
        
        layout.addWidget(QLabel("最高倍速（倍）:"), 0, 2)
        self.max_speed_spin = QDoubleSpinBox()
        self.max_speed_spin.setRange(0.1, 5.0)
        self.max_speed_spin.setSingleStep(0.1)
        self.max_speed_spin.setValue(1.2)
        layout.addWidget(self.max_speed_spin, 0, 3)
        
        # 缩放设置
        layout.addWidget(QLabel("最高放大比例（倍）:"), 1, 0)
        self.max_scale_spin = QDoubleSpinBox()
        self.max_scale_spin.setRange(1.0, 3.0)
        self.max_scale_spin.setSingleStep(0.1)
        self.max_scale_spin.setValue(1.0)
        layout.addWidget(self.max_scale_spin, 1, 1)
        
        # 视频尺寸
        layout.addWidget(QLabel("生成视频尺寸:"), 1, 2)
        self.video_size_combo = QComboBox()
        self.video_size_combo.addItems(["1080:1440", "1080:1920"])
        layout.addWidget(self.video_size_combo, 1, 3)
        
        # 素材重复阈值
        layout.addWidget(QLabel("素材重复阈值:"), 2, 0)
        self.repeat_scale_spin = QSpinBox()
        self.repeat_scale_spin.setRange(1, 10)
        self.repeat_scale_spin.setValue(1)
        layout.addWidget(self.repeat_scale_spin, 2, 1)

        # 算法选择
        layout.addWidget(QLabel("生成算法:"), 2, 2)
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.addItems([
            "传统算法",
            "优化算法(推荐)",
            "原增强算法(极严格)",
            "测试算法(实验性)"
        ])
        self.algorithm_combo.setCurrentIndex(1)  # 默认选择优化算法
        layout.addWidget(self.algorithm_combo, 2, 3)

        # 移除优化模式选择，固定使用质量优先模式

        # 隐藏目标生成数量设置，使用内部默认值
        self.target_count_spin = QSpinBox()
        self.target_count_spin.setRange(10, 10000)
        self.target_count_spin.setValue(500)  # 内部默认值，会根据素材数量自动调整
        self.target_count_spin.setVisible(False)  # 隐藏控件

         # 提示信息（第3行）
        tip_label = QLabel("*注：素材数量100以下设1，200-300设2，300以上设3")
        tip_label.setStyleSheet("color: red; font-size: 12px;")
        layout.addWidget(tip_label, 3, 0, 1, 3)  # 占据前3列

        # 历史感知选项（第3行，第3-5列）
        self.enable_history_aware = QCheckBox("启用历史感知生成")
        self.enable_history_aware.setChecked(True)  # 默认开启
        self.enable_history_aware.setToolTip("基于历史记录避免重复组合，提升生成数量和质量")
        layout.addWidget(self.enable_history_aware, 4, 0, 1, 6)  # 占据整行
        # 算法说明（第4行）
        algo_tip_label = QLabel("*优化算法：智能避免重复，质量优先；原增强：极致质量")
        algo_tip_label.setStyleSheet("color: blue; font-size: 12px;")
        layout.addWidget(algo_tip_label, 3, 3, 1, 3)
        # 连接信号
        self.video_size_combo.currentTextChanged.connect(self.on_video_size_changed)
        self.repeat_scale_spin.valueChanged.connect(self.on_repeat_scale_changed)
        
        parent_layout.addWidget(group_box)
    
    def create_subtitle_style_area(self, parent_layout):
        """创建字幕样式区域"""
        group_box = QGroupBox("字幕样式设置")
        layout = QGridLayout(group_box)
        
        # 字体设置
        layout.addWidget(QLabel("字体:"), 0, 0)
        self.font_combo = QComboBox()
        self.font_combo.setEditable(True)
        layout.addWidget(self.font_combo, 0, 1)
        
        layout.addWidget(QLabel("字幕大小:"), 0, 2)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setValue(11)
        layout.addWidget(self.font_size_spin, 0, 3)
        
        # 位置和颜色
        layout.addWidget(QLabel("字幕位置:"), 1, 0)
        self.font_position_spin = QSpinBox()
        self.font_position_spin.setRange(0, 200)
        self.font_position_spin.setValue(50)
        layout.addWidget(self.font_position_spin, 1, 1)
        
        layout.addWidget(QLabel("字幕颜色:"), 1, 2)
        self.font_color_btn = QPushButton()
        self.font_color_btn.setStyleSheet("background-color: white; border: 1px solid gray;")
        self.font_color = "#ffffff"
        layout.addWidget(self.font_color_btn, 1, 3)
        
        # 描边设置
        layout.addWidget(QLabel("描边大小:"), 2, 0)
        self.outline_size_spin = QSpinBox()
        self.outline_size_spin.setRange(0, 10)
        self.outline_size_spin.setValue(1)
        layout.addWidget(self.outline_size_spin, 2, 1)
        
        layout.addWidget(QLabel("描边颜色:"), 2, 2)
        self.outline_color_btn = QPushButton()
        self.outline_color_btn.setStyleSheet("background-color: black; border: 1px solid gray;")
        self.outline_color = "#000000"
        layout.addWidget(self.outline_color_btn, 2, 3)
        
        # 连接信号
        self.font_color_btn.clicked.connect(self.select_font_color)
        self.outline_color_btn.clicked.connect(self.select_outline_color)
        
        parent_layout.addWidget(group_box)
    
    def create_control_area(self, parent_layout):
        """创建控制区域"""
        group_box = QGroupBox("操作控制")
        layout = QHBoxLayout(group_box)
        
        # 计算按钮
        self.calculate_btn = QPushButton("计算视频生成数")
        self.calculate_btn.setMinimumHeight(40)
        layout.addWidget(self.calculate_btn)
        
        # 生成数量显示
        self.generation_label = QLabel("可生成数：0条")
        self.generation_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        layout.addWidget(self.generation_label)
        
        # 生成按钮
        self.generate_btn = QPushButton("生成视频")
        self.generate_btn.setMinimumHeight(40)
        self.generate_btn.setEnabled(False)
        layout.addWidget(self.generate_btn)
        
        # 历史统计按钮（暂时隐藏）
        self.history_stats_btn = QPushButton("📊 历史统计")
        self.history_stats_btn.setMinimumHeight(40)
        self.history_stats_btn.setToolTip("查看素材使用统计和生成历史")
        self.history_stats_btn.setVisible(False)  # 暂时隐藏
        layout.addWidget(self.history_stats_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 连接信号
        self.calculate_btn.clicked.connect(self.calculate_generation_count)
        self.generate_btn.clicked.connect(self.start_generation)
        self.history_stats_btn.clicked.connect(self.show_history_stats)
        
        parent_layout.addWidget(group_box)
    
    def create_log_area(self, parent_layout):
        """创建日志区域"""
        group_box = QGroupBox("运行日志")
        layout = QVBoxLayout(group_box)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        parent_layout.addWidget(group_box)
    
    def set_style(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #2c5281;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a6b9e;
            }
            QPushButton:pressed {
                background-color: #1e3a5f;
            }
            QPushButton:disabled {
                background-color: #83b5e8;
            }
        """)
    
    def load_settings(self):
        """加载设置"""
        # 加载系统字体
        font_database = QFontDatabase()
        font_families = font_database.families()
        self.font_combo.addItems(font_families)
        
        # 设置默认字体
        if font_families:
            self.font_combo.setCurrentText(font_families[0])
        
        # 加载配置
        video_config = self.config.get_video_config()
        subtitle_config = self.config.get_subtitle_config()
        
        self.min_speed_spin.setValue(video_config.get('min_speed', 0.9))
        self.max_speed_spin.setValue(video_config.get('max_speed', 1.1))
        self.max_scale_spin.setValue(video_config.get('max_scale', 1.0))
        self.repeat_scale_spin.setValue(video_config.get('repeat_scale', 1))
        
        self.font_size_spin.setValue(subtitle_config.get('font_size', 11))
        self.font_position_spin.setValue(subtitle_config.get('bottom_margin', 50))
        
        # 显示设备ID
        self.log(f"设备ID: {self.device_id}")
    
    def log(self, message: str):
        """添加日志信息"""
        self.log_text.append(f"[{self.get_current_time()}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    # 文件选择相关方法将在下一部分实现
    def select_subtitle_file(self):
        """选择字幕文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择SRT字幕文件", "", "SRT文件 (*.srt)"
        )
        
        if file_path:
            self.log(f"正在解析字幕文件: {file_path}")
            self.subtitles = self.subtitle_parser.parse_srt_file(file_path)

            if self.subtitles:
                self.subtitle_path = file_path  # 保存字幕文件路径
                self.subtitle_label.setText(os.path.basename(file_path))
                self.subtitle_count_label.setText(f"字幕片段数：{len(self.subtitles)}条")
                self.log(f"成功解析 {len(self.subtitles)} 条字幕")
                
                # 重置生成数量
                self.generation_count = 0
                self.generation_label.setText("可生成数：0条")
                self.generate_btn.setEnabled(False)
            else:
                QMessageBox.warning(self, "错误", "字幕文件解析失败！")
    
    def select_audio_file(self):
        """选择音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "", "音频文件 (*.mp3 *.wav *.aac)"
        )
        
        if file_path:
            self.audio_path = file_path
            self.audio_label.setText(os.path.basename(file_path))

            # 获取音频时长
            self.log(f"正在获取音频时长: {file_path}")
            self.audio_duration = self.video_processor.get_audio_duration(file_path)

            if self.audio_duration > 0:
                self.audio_duration_label.setText(f"音频时长：{self.audio_duration:.1f}秒")
                self.log(f"音频时长获取成功: {self.audio_duration:.1f}秒")
            else:
                self.audio_duration_label.setText("音频时长：获取失败")
                self.log("音频时长获取失败，请检查文件格式")

            self.log(f"选择音频文件: {file_path}")

            # 重置生成数量
            self.generation_count = 0
            self.generation_label.setText("可生成数：0条")
            self.generate_btn.setEnabled(False)
    
    def select_video_files(self):
        """选择视频文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "", "视频文件 (*.mp4 *.avi *.mov)"
        )
        
        if file_paths:
            self.log(f"正在分析 {len(file_paths)} 个视频文件...")
            self.videos = []
            
            for file_path in file_paths:
                video_info = self.video_processor.get_video_info(file_path)
                if video_info:
                    self.videos.append(video_info)
            
            if self.videos:
                self.video_label.setText(f"已选择 {len(self.videos)} 个视频文件")
                self.video_count_label.setText(f"视频素材数：{len(self.videos)}条")
                self.log(f"成功分析 {len(self.videos)} 个视频文件")

                # 自动更新目标数量
                smart_target = self._get_smart_target_count()
                self.target_count_spin.setValue(smart_target)
                self.log(f"根据素材数量自动设置目标: {smart_target}个视频")

                # 重置生成数量
                self.generation_count = 0
                self.generation_label.setText("可生成数：0条")
                self.generate_btn.setEnabled(False)
            else:
                QMessageBox.warning(self, "错误", "没有有效的视频文件！")
    
    def select_export_folder(self):
        """选择导出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择导出文件夹")
        
        if folder_path:
            self.export_path = folder_path
            self.export_label.setText(folder_path)
            self.log(f"选择导出文件夹: {folder_path}")
    
    def select_font_color(self):
        """选择字体颜色"""
        color = QColorDialog.getColor(QColor(self.font_color), self)
        if color.isValid():
            self.font_color = color.name()
            self.font_color_btn.setStyleSheet(
                f"background-color: {self.font_color}; border: 1px solid gray;"
            )
    
    def select_outline_color(self):
        """选择描边颜色"""
        color = QColorDialog.getColor(QColor(self.outline_color), self)
        if color.isValid():
            self.outline_color = color.name()
            self.outline_color_btn.setStyleSheet(
                f"background-color: {self.outline_color}; border: 1px solid gray;"
            )
    
    def on_video_size_changed(self, size_text: str):
        """视频尺寸改变"""
        if ":" in size_text:
            width, height = size_text.split(":")
            self.config.set("video.width", int(width))
            self.config.set("video.height", int(height))
            
            # 重置生成数量
            self.generation_count = 0
            self.generation_label.setText("可生成数：0条")
            self.generate_btn.setEnabled(False)
    
    def on_repeat_scale_changed(self, value: int):
        """重复阈值改变"""
        # 重置生成数量
        self.generation_count = 0
        self.generation_label.setText("可生成数：0条")
        self.generate_btn.setEnabled(False)
        self.log(f"素材重复阈值已设置为: {value}")
    
    def calculate_generation_count(self):
        """计算视频生成数量"""
        if not self.subtitles:
            QMessageBox.warning(self, "警告", "请先选择字幕文件！")
            return

        if not self.videos:
            QMessageBox.warning(self, "警告", "请先选择视频文件！")
            return

        if not self.audio_path:
            QMessageBox.warning(self, "警告", "请先选择音频文件！")
            return

        # 禁用计算按钮，显示计算中状态
        self.calculate_btn.setEnabled(False)
        self.calculate_btn.setText("计算中...")

        # 获取选择的算法
        algorithm_choice = self.algorithm_combo.currentText()

        # 根据选择的算法进行计算
        if "测试算法" in algorithm_choice:
            self.log("🧪 正在使用测试算法计算视频生成数量，请稍等...")
            self._start_calculation_thread("test_algorithm")
        elif "原增强算法" in algorithm_choice:
            self.log("🔄 正在使用原增强算法计算视频生成数量，请稍等...")
            self._start_calculation_thread("original_enhanced")
        elif "智能增强算法" in algorithm_choice:
            self.log("🔄 正在使用智能增强算法计算视频生成数量，请稍等...")
            self._start_calculation_thread("enhanced")
        elif "优化算法" in algorithm_choice:
            if "快速模式" in algorithm_choice:
                self.log("🔄 正在使用优化算法(快速模式)计算视频生成数量，请稍等...")
                self._start_calculation_thread("optimized_fast")
            else:
                self.log("🔄 正在使用优化算法计算视频生成数量，请稍等...")
                self._start_calculation_thread("optimized")
        else:
            self.log("🔄 正在使用传统算法计算视频生成数量，请稍等...")
            self._start_calculation_thread("traditional")

        # 如果启用历史感知，添加提示
        if self.enable_history_aware.isChecked():
            self.log("📊 已启用历史感知功能，将在生成时进行去重筛选")

    def _start_calculation_thread(self, algorithm_type):
        """启动计算线程"""
        self.calculation_thread = CalculationThread(
            algorithm_type, self.videos, self.subtitles, self.audio_path, self
        )
        self.calculation_thread.calculation_finished.connect(self._on_calculation_finished)
        self.calculation_thread.calculation_error.connect(self._on_calculation_error)
        self.calculation_thread.calculation_progress.connect(self._on_calculation_progress)
        self.calculation_thread.start()

    def _on_calculation_finished(self, count, algorithm_type):
        """计算完成处理"""
        self.generation_count = count
        self.generation_label.setText(f"可生成数：{count}条")

        # 恢复计算按钮
        self.calculate_btn.setEnabled(True)
        self.calculate_btn.setText("计算生成数")

        # 设置match_results以启用生成按钮
        if count > 0:
            self.log(f"🔍 计算线程返回count: {count}")
            try:
                if algorithm_type == "traditional":
                    # 传统算法直接使用已有的计算结果
                    self._recalculate_for_generation("traditional")
                    self.log(f"✅ 传统算法计算完成！可生成 {count} 条视频")
                elif algorithm_type == "test_algorithm":
                    # 使用测试算法重新计算获取结果
                    self._recalculate_for_generation("test_algorithm")
                    self.log(f"✅ 测试算法计算完成！可生成 {count} 条视频")
                elif algorithm_type == "original_enhanced":
                    # 使用原增强算法重新计算获取结果
                    self._recalculate_for_generation("original_enhanced")
                else:
                    # 使用优化算法重新计算获取结果
                    self._recalculate_for_generation("optimized")

                if self.match_results:
                    actual_count = len(self.match_results)
                    self.log(f"🔍 重新计算后match_results长度: {actual_count}")

                    # 如果启用历史感知，立即进行过滤
                    if self.enable_history_aware.isChecked():
                        self.log("📊 启用历史感知功能，正在计算去重后数量...")
                        filtered_results = self._apply_history_aware_filter(self.match_results)
                        if len(filtered_results) < len(self.match_results):
                            skipped_count = len(self.match_results) - len(filtered_results)
                            self.log(f"🔍 历史感知筛选：跳过 {skipped_count} 个重复组合")
                            self.match_results = filtered_results
                            actual_count = len(filtered_results)

                            # 更新显示的可生成数量
                            self.generation_count = actual_count
                            self.generation_label.setText(f"可生成数：{actual_count}条")

                            if actual_count == 0:
                                self.generate_btn.setEnabled(False)
                                self.log("⚠️ 所有组合都已存在于历史记录中，无新视频可生成！")
                            else:
                                self.generate_btn.setEnabled(True)
                                self.log(f"✅ 历史感知筛选后可生成 {actual_count} 条视频")
                        else:
                            self.generate_btn.setEnabled(True)
                            self.log("✅ 历史感知筛选：所有组合都是新的")
                    else:
                        self.generate_btn.setEnabled(True)
                        if algorithm_type != "traditional":
                            self.log(f"✅ 计算完成！可生成 {actual_count} 条视频")
                else:
                    self.generate_btn.setEnabled(False)
                    self.log("❌ 无法获取生成方案，请重新计算")
            except Exception as e:
                self.match_results = None
                self.generate_btn.setEnabled(False)
                self.log(f"❌ 获取生成方案失败: {e}")
        else:
            self.match_results = None
            self.generate_btn.setEnabled(False)
            self.log("❌ 无法生成视频，请检查素材和字幕配置")

    def _on_calculation_progress(self, progress, message):
        """计算进度处理"""
        # 更新按钮文本显示进度
        if progress > 0:
            self.calculate_btn.setText(f"计算中... {progress:.1f}%")
        else:
            self.calculate_btn.setText("计算中...")

        # 记录进度到日志
        self.log(f"📊 {message}")

    def _on_calculation_error(self, error_msg):
        """计算错误处理"""
        # 恢复计算按钮
        self.calculate_btn.setEnabled(True)
        self.calculate_btn.setText("计算生成数")

        self.log(f"❌ 计算失败：{error_msg}")
        QMessageBox.critical(self, "计算错误", f"计算过程中发生错误：\n{error_msg}")

    def _recalculate_for_generation(self, algorithm_type):
        """重新计算以获取实际的match_results用于生成"""
        try:
            # 确保使用相同的随机种子
            import random
            import numpy as np
            random.seed(42)
            np.random.seed(42)

            if algorithm_type == "traditional":
                # 使用传统算法
                self._calculate_with_traditional_algorithm()

            elif algorithm_type == "test_algorithm":
                # 使用测试算法
                from core.test_algorithm import TestVideoGenerator
                generator = TestVideoGenerator(
                    num_materials=len(self.videos),
                    segments_per_video=len(self.subtitles),
                    enable_similarity_check=False,
                    optimization_mode="balanced",
                    debug_mode=False
                )

                # 设置素材
                generator.set_materials(self.videos)

                # 设置数据库管理器
                if hasattr(self, 'db_manager') and self.db_manager:
                    material_md5s = []
                    for video in self.videos:
                        import hashlib
                        material_md5s.append(hashlib.md5(video.path.encode()).hexdigest())
                    generator.set_database_manager(self.db_manager, material_md5s)

                max_videos = self.target_count_spin.value()
                count, generated_videos = generator.generate_optimal_videos_test(max_videos)

                self.log(f"🔍 测试算法重新计算结果数量: {len(generated_videos) if generated_videos else 0}")

                # 转换为传统格式
                self.match_results = self._convert_optimized_results_to_traditional(generated_videos)

                # 保存生成的视频信息，用于数据库记录
                self.generated_videos_for_recording = generated_videos

            elif algorithm_type == "original_enhanced":
                # 使用原增强算法
                from core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator
                generator = OriginalEnhancedVideoGenerator(
                    num_materials=len(self.videos),
                    segments_per_video=len(self.subtitles),
                    enable_similarity_check=False,
                    optimization_mode="quality"
                )

                # 设置数据库管理器
                if hasattr(self, 'db_manager') and self.db_manager:
                    material_md5s = []
                    for video in self.videos:
                        import hashlib
                        material_md5s.append(hashlib.md5(video.path.encode()).hexdigest())
                    generator.set_database_manager(self.db_manager, material_md5s)

                max_videos = self.target_count_spin.value()
                count, generated_videos = generator.generate_optimal_videos_original_enhanced(max_videos)

                self.log(f"🔍 重新计算结果数量: {len(generated_videos) if generated_videos else 0}")

                # 转换为传统格式
                self.match_results = self._convert_optimized_results_to_traditional(generated_videos)

                # 保存生成的视频信息，用于数据库记录
                self.generated_videos_for_recording = generated_videos

            else:
                # 使用优化算法，直接调用现有方法
                self._calculate_with_optimized_algorithm()

        except Exception as e:
            self.log(f"重新计算失败: {e}")
            self.match_results = None

    def _record_generation_to_database(self, generated_videos, output_dir):
        """记录生成的视频到历史数据库"""
        try:
            from core.video_history_database import VideoHistoryDB, CombinationSequence
            from datetime import datetime
            import uuid

            # 创建数据库连接
            db = VideoHistoryDB()
            if not db.connect():
                self.log("❌ 数据库连接失败")
                return False

            # 生成批次ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            batch_id = f"batch_{timestamp}_{str(uuid.uuid4())[:8]}"

            # 获取素材MD5映射
            material_md5_map = {}
            self.log(f"🔍 开始计算{len(self.videos)}个素材的MD5...")
            for i, video in enumerate(self.videos):
                self.log(f"🔍 计算素材{i}: {video.path}")
                md5_hash = db.record_material(video.path)
                if md5_hash:
                    material_md5_map[i] = md5_hash
                    self.log(f"✅ 素材{i} MD5: {md5_hash}")
                else:
                    self.log(f"❌ 素材{i} MD5计算失败")

            self.log(f"🔍 准备记录{len(generated_videos)}个视频到数据库")
            self.log(f"🔍 素材MD5映射: {len(material_md5_map)}个")
            self.log(f"🔍 素材总数: {len(self.videos)}个")

            success_count = 0
            for i, video in enumerate(generated_videos):
                try:
                    # 构建视频路径
                    video_path = os.path.join(output_dir, f"{i + 1}.mp4")

                    # 提取素材MD5和位置
                    material_md5s = []
                    material_indices = []
                    positions = []

                    for segment in video.segments:
                        # 验证segment索引是否在有效范围内
                        if segment.video_index >= len(self.videos):
                            self.log(f"⚠️ 视频{i+1}的segment索引{segment.video_index}超出范围(最大{len(self.videos)-1})")
                            continue

                        if segment.video_index in material_md5_map:
                            material_md5s.append(material_md5_map[segment.video_index])
                            material_indices.append(segment.video_index)
                            positions.append(segment.position)
                        else:
                            self.log(f"⚠️ 视频{i+1}的segment {segment.video_index}没有MD5映射")

                    if len(material_md5s) == len(video.segments):
                        # 创建组合序列
                        combination_sequence = CombinationSequence(
                            material_indices=material_indices,
                            positions=positions
                        )

                        # 计算质量分数
                        quality_score = 1.0 - (i / len(generated_videos)) * 0.3

                        # 记录生成的视频
                        if db.record_generated_video(
                            video_path, combination_sequence, material_md5s,
                            batch_id, quality_score
                        ):
                            success_count += 1
                        else:
                            self.log(f"⚠️ 视频{i+1}记录失败")
                    else:
                        self.log(f"⚠️ 视频{i+1}素材MD5不完整: {len(material_md5s)}/{len(video.segments)}")

                except Exception as e:
                    self.log(f"❌ 记录第{i+1}个视频失败: {e}")
                    continue

            # 创建批次记录
            algorithm_type = "traditional" if hasattr(self, 'current_algorithm') and self.current_algorithm == "traditional" else "optimized"
            if success_count > 0:
                db.create_batch_record(
                    batch_id,
                    len(generated_videos),
                    success_count,
                    algorithm_type
                )
                self.log(f"✅ 成功记录 {success_count}/{len(generated_videos)} 个视频到数据库")
            else:
                self.log(f"❌ 没有视频成功记录到数据库")

            self.log(f"🔍 数据库记录完成: {success_count}/{len(generated_videos)}")
            db.close()
            return success_count == len(generated_videos)

        except Exception as e:
            self.log(f"❌ 数据库记录失败: {e}")
            return False

    def _calculate_with_optimized_algorithm_for_thread(self, algorithm_type):
        """为线程提供的优化算法计算（保持原有逻辑）"""
        # 设置固定随机种子，确保一致性
        import random
        import numpy as np
        random.seed(42)
        np.random.seed(42)

        # 固定使用质量优先模式
        optimization_mode = "quality"

        # 根据算法类型选择生成器
        if algorithm_type == "original_enhanced":
            from core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator
            current_generator = OriginalEnhancedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode
            )

        else:  # optimized
            from core.algorithm import OptimizedVideoGenerator
            current_generator = OptimizedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode
            )

        # 设置数据库管理器
        self._setup_database_for_generator(current_generator)

        # 使用用户设置的目标数量，不再有理论限制
        max_videos = self.target_count_spin.value()
        print(f"🎯 用户设置目标: {max_videos}个视频")

        try:
            # 根据算法类型调用相应的生成方法
            if algorithm_type == "original_enhanced":
                count, generated_videos = current_generator.generate_optimal_videos_original_enhanced(max_videos)

            else:  # optimized
                count, generated_videos = current_generator.generate_optimal_videos(max_videos)

            return count

        except Exception as e:
            print(f"算法计算失败: {str(e)}")
            # 返回理论最大值作为备用
            return min(max_videos, 50)

    def _setup_database_for_generator(self, generator):
        """为生成器设置数据库管理器"""
        try:
            # 导入数据库管理器
            from core.database import VideoGenerationDB

            # 创建数据库管理器
            db_manager = VideoGenerationDB()

            # 计算素材MD5
            material_md5s = []
            for video in self.videos:
                try:
                    import hashlib
                    with open(video.path, 'rb') as f:
                        # 只读取文件的前1MB来计算MD5（提高速度）
                        content = f.read(1024 * 1024)
                        md5_hash = hashlib.md5(content).hexdigest()
                        material_md5s.append(md5_hash)
                except Exception as e:
                    self.log(f"⚠️ 计算素材MD5失败 {video.path}: {e}")
                    # 使用文件路径作为备用标识
                    material_md5s.append(hashlib.md5(video.path.encode()).hexdigest())

            # 设置数据库管理器
            generator.set_database_manager(db_manager, material_md5s)
            self.log(f"🔗 已启用数据库重复检查，{len(material_md5s)}个素材")

        except Exception as e:
            self.log(f"⚠️ 数据库连接失败，跳过重复检查: {e}")
            # 数据库连接失败时不影响正常功能

    def _calculate_with_traditional_algorithm(self):
        """使用传统算法计算"""
        # 设置匹配参数
        size_text = self.video_size_combo.currentText()
        width, height = map(int, size_text.split(":"))

        self.video_matcher.set_parameters(
            min_speed=self.min_speed_spin.value(),
            max_speed=self.max_speed_spin.value(),
            max_scale=self.max_scale_spin.value(),
            repeat_scale=self.repeat_scale_spin.value(),
            output_width=width,
            output_height=height
        )

        # 计算生成数量
        count, results = self.video_matcher.compute_generation_count(self.subtitles, self.videos)

        self.generation_count = count
        self.match_results = results
        self.generation_label.setText(f"可生成数：{count}条")

        if count > 0:
            self.generate_btn.setEnabled(True)
            self.log(f"传统算法计算完成，可生成 {count} 条视频")

            # 显示素材使用统计
            usage = self.video_matcher.analyze_material_usage(results)
            self.log(f"素材使用统计: {len(usage)} 个素材将被使用")
        else:
            self.generate_btn.setEnabled(False)
            self.log("素材数量或时长不足，无法生成视频")

    def _calculate_with_optimized_algorithm(self):
        """使用优化算法计算"""
        # 设置固定随机种子，确保每次计算结果一致
        import random
        import numpy as np
        random.seed(42)
        np.random.seed(42)

        # 固定使用质量优先模式
        optimization_mode = "quality"

        # 根据算法选择和历史感知选项选择生成器
        algorithm_text = self.algorithm_combo.currentText()

        if "测试算法" in algorithm_text:
            self.log("启用测试算法...")
            self.test_generator = TestVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode,
                debug_mode=False
            )
            current_generator = self.test_generator
        elif "原增强算法" in algorithm_text:
            self.log("启用原增强算法...")
            self.original_enhanced_generator = OriginalEnhancedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,
                optimization_mode=optimization_mode
            )
            current_generator = self.original_enhanced_generator
        else:
            # 检查是否是快速模式
            if "快速模式" in algorithm_text:
                self.log("启用优化算法(快速模式)...")
                optimization_mode = "fast"
            else:
                self.log("启用优化算法...")

            # 重新创建优化生成器以应用新模式
            self.optimized_generator = OptimizedVideoGenerator(
                num_materials=len(self.videos),
                segments_per_video=len(self.subtitles),
                enable_similarity_check=False,  # 禁用不靠谱的相似性检测
                optimization_mode=optimization_mode
            )
            current_generator = self.optimized_generator

        # 设置数据库管理器（所有算法都需要）
        self._setup_database_for_generator(current_generator)

        # 相似性检测已移除，因为基于文件名/路径的检测不可靠

        # 素材质量评估
        self._evaluate_material_quality()

        # 使用用户设置的目标数量，完全移除理论限制
        max_videos = self.target_count_spin.value()
        self.log(f"🎯 目标生成数量: {max_videos}个（用户设置）")

        try:
            algorithm_text = self.algorithm_combo.currentText()

            # 根据选择的算法生成视频
            if "测试算法" in algorithm_text:
                self.log("🧪 使用测试算法生成视频...")
                # 为测试算法设置素材
                current_generator.set_materials(self.videos)
                count, generated_videos = current_generator.generate_optimal_videos_test(max_videos)
            elif "原增强算法" in algorithm_text:
                self.log("🔄 使用原增强算法生成视频...")
                count, generated_videos = current_generator.generate_optimal_videos_original_enhanced(max_videos)
            elif "智能增强算法" in algorithm_text:
                self.log("🔄 使用智能增强算法生成视频...")
                count, generated_videos = current_generator.generate_optimal_videos_enhanced(max_videos)
            else:
                self.log("🔄 使用优化算法生成视频...")
                count, generated_videos = current_generator.generate_optimal_videos(max_videos)

            # 保存生成的视频信息，用于生成成功后记录到数据库
            self.generated_videos_for_recording = generated_videos

            # 转换为传统格式以兼容现有代码
            self.match_results = self._convert_optimized_results_to_traditional(generated_videos)
            self.generation_count = count
            self.generation_label.setText(f"可生成数：{count}条")

            if count > 0:
                self.generate_btn.setEnabled(True)
                self.log(f"优化算法计算完成，可生成 {count} 条视频")
                self.log(f"素材利用率: {count/len(self.videos):.1f}倍")
                self.log(f"最大重复率: ≤30%，确保通过审核")
            else:
                self.generate_btn.setEnabled(False)
                self.log("优化算法无法生成视频，请检查素材数量")

        except Exception as e:
            self.log(f"优化算法计算失败: {str(e)}")
            self.log("自动切换到传统算法...")
            self._calculate_with_traditional_algorithm()

    def _calculate_theoretical_max_videos(self) -> int:
        """计算理论最大视频生成数量（基于传统算法逻辑）"""
        if not self.subtitles or not self.videos:
            return 0

        # 检查基本约束
        if len(self.subtitles) > len(self.videos):
            self.log(f"字幕数量({len(self.subtitles)})大于素材数量({len(self.videos)})，无法生成视频")
            return 0

        # 检查素材时长是否足够
        min_subtitle_duration = min(subtitle.duration_ms / 1000.0 for subtitle in self.subtitles)
        max_subtitle_duration = max(subtitle.duration_ms / 1000.0 for subtitle in self.subtitles)

        valid_materials = 0
        for video in self.videos:
            # 考虑最低速度调整后的最长可用时长（最坏情况）
            max_available_duration = video.duration / self.min_speed_spin.value()
            if max_available_duration >= max_subtitle_duration:
                valid_materials += 1

        if valid_materials < len(self.subtitles):
            self.log(f"有效素材数量({valid_materials})不足，需要至少{len(self.subtitles)}个")
            return min(50, valid_materials)  # 返回一个保守的数量

        # 计算更宽松的理论最大数量
        # 不再基于重复率限制，而是基于实际可能的组合数

        # 基础计算：每个素材平均可以使用多少次
        if valid_materials >= len(self.subtitles) * 3:
            # 大量素材：每个素材可以使用10-15次
            repeat_factor = 12
        elif valid_materials >= len(self.subtitles) * 2:
            # 中等素材：每个素材可以使用8-10次
            repeat_factor = 9
        else:
            # 少量素材：每个素材可以使用5-8次
            repeat_factor = 6

        # 理论最大 = (有效素材数 × 重复因子) ÷ 每视频片段数
        theoretical_max = (valid_materials * repeat_factor) // len(self.subtitles)

        # 设置更宽松的上限，让算法自己探索极限
        practical_max = min(theoretical_max, 500)  # 从200提高到500

        self.log(f"素材分析: {valid_materials}/{len(self.videos)} 个素材时长足够")
        self.log(f"理论最大生成数量: {theoretical_max} (实际限制: {practical_max})")
        self.log(f"💡 注意：实际生成数量由算法重复检测决定，可能少于理论值")

        return practical_max

    def _convert_optimized_results_to_traditional(self, generated_videos):
        """将优化算法结果转换为传统格式"""
        from core.algorithm import MatchResult, VideoCandidate
        import random

        traditional_results = []

        for video in generated_videos:
            video_matches = []

            # 按位置排序片段
            sorted_segments = sorted(video.segments, key=lambda x: x.position)

            for i, segment in enumerate(sorted_segments):
                # 确保有对应的字幕
                if i < len(self.subtitles):
                    subtitle = self.subtitles[i]

                    # 确保有对应的视频素材
                    if segment.video_index < len(self.videos):
                        video_info = self.videos[segment.video_index]

                        # 计算合适的参数
                        subtitle_duration_sec = subtitle.duration_ms / 1000.0

                        # 时长随机化：通过播放速度调整实现，±15%变化
                        # 限制变化范围，避免过度加速或减速导致视觉问题
                        duration_variation = random.uniform(-0.15, 0.15)
                        adjusted_duration = subtitle_duration_sec * (1 + duration_variation)
                        # 限制最小0.8秒，最大不超过原时长的1.3倍
                        adjusted_duration = max(0.8, min(adjusted_duration, subtitle_duration_sec * 1.3))

                        # 使用传统算法的参数范围
                        speed_scale = random.uniform(
                            self.min_speed_spin.value(),
                            self.max_speed_spin.value()
                        )
                        size_scale = random.uniform(1.0, self.max_scale_spin.value())

                        # 计算调整速度后的视频时长
                        new_duration = video_info.duration / speed_scale

                        # 确保开始时间合理（使用调整后的时长）
                        max_start_time = max(0, new_duration - adjusted_duration)
                        start_offset = random.uniform(0, max_start_time) if max_start_time > 0 else 0

                        # 创建VideoCandidate
                        video_candidate = VideoCandidate(
                            video_info=video_info,
                            source_index=segment.video_index,
                            speed_scale=speed_scale,
                            size_scale=size_scale,
                            new_duration=new_duration
                        )

                        # 创建MatchResult（包含调整后的时长）
                        match_result = MatchResult(
                            subtitle=subtitle,
                            video_candidate=video_candidate,
                            start_offset=start_offset,
                            adjusted_duration=adjusted_duration
                        )

                        video_matches.append(match_result)

            if video_matches:
                traditional_results.append(video_matches)

        return traditional_results

    def _evaluate_material_quality(self):
        """评估素材质量并给出建议"""
        if not self.videos:
            return
        # 生成基础建议
        suggestions = []
        if len(self.videos) < 15:
            suggestions.append("💡 建议：增加素材数量到15-20个以提升通过率")
        # 显示评估结果
        if suggestions:
            self.log("=== 素材质量评估 ===")
            for suggestion in suggestions:
                self.log(suggestion)
        else:
            self.log("✅ 素材配置良好")

    def _analyze_source_diversity(self, material_paths: List[str]) -> float:
        """分析素材来源多样性，返回0-1的分数"""
        if not material_paths:
            return 0.0

        # 提取目录路径
        directories = set()
        file_prefixes = set()

        for path in material_paths:
            # 目录多样性
            directories.add(os.path.dirname(path))

            # 文件名前缀多样性（前3个字符）
            filename = os.path.splitext(os.path.basename(path))[0]
            if len(filename) >= 3:
                file_prefixes.add(filename[:3].lower())

        # 计算多样性分数
        dir_diversity = len(directories) / len(material_paths)
        prefix_diversity = len(file_prefixes) / len(material_paths)

        # 综合分数
        diversity_score = (dir_diversity * 0.6 + prefix_diversity * 0.4)
        return min(1.0, diversity_score)

    def _apply_history_aware_filter(self, match_results):
        """应用历史感知过滤，移除重复组合"""
        try:
            from core.video_history_database import VideoHistoryDB, CombinationSequence

            # 创建数据库连接
            db = VideoHistoryDB()
            if not db.connect():
                self.log("⚠️ 历史数据库连接失败，跳过去重筛选")
                return match_results

            filtered_results = []

            for result_group in match_results:
                # 从match_result提取组合信息
                material_indices = []
                positions = []

                for i, match_result in enumerate(result_group):
                    # 获取视频索引
                    video_path = match_result.video_candidate.video_info.path
                    video_index = -1
                    for j, video in enumerate(self.videos):
                        if video.path == video_path:
                            video_index = j
                            break

                    if video_index >= 0:
                        material_indices.append(video_index)
                        positions.append(i)  # 使用在字幕中的位置

                if material_indices:
                    # 创建组合序列
                    combination = CombinationSequence(
                        material_indices=material_indices,
                        positions=positions
                    )

                    # 检查是否已存在
                    if not db.is_combination_used(combination):
                        filtered_results.append(result_group)

            db.close()
            return filtered_results

        except Exception as e:
            self.log(f"❌ 历史感知筛选失败: {e}")
            return match_results  # 失败时返回原始结果

    def start_generation(self):
        """开始生成视频"""
        if not self.export_path:
            QMessageBox.warning(self, "警告", "请先选择导出文件夹！")
            return

        if not self.match_results:
            QMessageBox.warning(self, "警告", "请先计算视频生成数量！")
            return

        # 检查授权状态
        if not self.is_logged_in or self.auth_manager.is_expired():
            QMessageBox.warning(self, "警告", "授权已过期，请重新验证！")
            return

        # 历史感知筛选已在计算阶段完成，这里只需要记录日志
        if self.enable_history_aware.isChecked():
            self.log("📊 历史感知功能已启用，使用计算阶段筛选后的结果")

        # 准备字幕样式
        subtitle_style = {
            'font_family': self.font_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            'bottom_margin': self.font_position_spin.value(),
            'color': self.font_color,
            'outline_color': self.outline_color,
            'outline_size': self.outline_size_spin.value()
        }

        # 创建并启动生成线程
        self.generation_thread = VideoGenerationThread(
            self.match_results,
            self.audio_path,
            self.subtitle_path,
            self.export_path,
            subtitle_style,
            self.audio_duration
        )

        # 连接信号
        self.generation_thread.progress_updated.connect(self.on_generation_progress)
        self.generation_thread.video_completed.connect(self.on_video_completed)
        self.generation_thread.generation_finished.connect(self.on_generation_finished)
        self.generation_thread.log_message.connect(self.log)

        # 更新UI状态
        self.generate_btn.setText("取消生成")
        self.generate_btn.clicked.disconnect()
        self.generate_btn.clicked.connect(self.cancel_generation)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动线程
        self.generation_thread.start()
        self.log("视频生成已启动...")

    def cancel_generation(self):
        """取消视频生成"""
        if self.generation_thread and self.generation_thread.isRunning():
            self.generation_thread.cancel()
            self.log("正在取消视频生成...")

    def on_generation_progress(self, progress: int, message: str):
        """处理生成进度更新"""
        self.progress_bar.setValue(progress)
        self.log(f"进度 {progress}%: {message}")

    def on_video_completed(self, video_number: int, output_path: str):
        """处理单个视频完成"""
        self.log(f"视频 {video_number} 生成完成: {output_path}")

    def on_generation_finished(self, success: bool, message: str):
        """处理生成完成"""
        # 恢复UI状态
        self.generate_btn.setText("生成视频")
        self.generate_btn.clicked.disconnect()
        self.generate_btn.clicked.connect(self.start_generation)
        self.progress_bar.setVisible(False)

        # 显示结果
        if success:
            self.log(f"视频生成成功: {message}")

            # 从message中提取output_dir
            output_dir = None
            if "成功生成" in message and "到:" in message:
                output_dir = message.split("到: ")[-1]

            # 记录成功的生成结果到数据库
            if hasattr(self, 'generated_videos_for_recording') and self.generated_videos_for_recording:
                self.log("正在记录生成结果到数据库...")
                try:
                    # 根据使用的生成器类型选择记录方法
                    if (self.enable_history_aware.isChecked() and
                        hasattr(self, 'history_aware_generator') and
                        self.history_aware_generator):
                        # 使用历史感知生成器记录
                        record_success = self.history_aware_generator.record_successful_generation(
                            self.generated_videos_for_recording
                        )
                    else:
                        # 使用通用数据库记录方法
                        if output_dir:
                            record_success = self._record_generation_to_database(
                                self.generated_videos_for_recording, output_dir
                            )
                        else:
                            self.log("⚠️ 无法获取输出目录，跳过数据库记录")
                            record_success = False

                    if record_success:
                        self.log("✅ 生成结果已记录到数据库")
                    else:
                        self.log("⚠️ 部分生成结果记录失败")
                except Exception as e:
                    self.log(f"❌ 记录生成结果失败: {e}")

                # 清理临时变量
                delattr(self, 'generated_videos_for_recording')

            QMessageBox.information(self, "成功", message)
        else:
            self.log(f"视频生成失败: {message}")
            QMessageBox.warning(self, "失败", message)

        # 清理线程
        if self.generation_thread:
            self.generation_thread.deleteLater()
            self.generation_thread = None

    def show_history_stats(self):
        """显示历史统计窗口"""
        try:
            # 创建历史统计窗口
            self.history_stats_window = HistoryStatsWidget()
            self.history_stats_window.setWindowTitle("历史生成统计")
            self.history_stats_window.resize(800, 600)

            # 显示窗口并刷新数据
            self.history_stats_window.show()
            self.history_stats_window.refresh_stats()

            self.log("已打开历史统计窗口")

        except Exception as e:
            self.log(f"打开历史统计窗口失败: {e}")
            QMessageBox.warning(self, "错误", f"无法打开历史统计窗口：{e}")

    def _update_algorithm_info(self):
        """更新算法说明信息"""
        algorithm_text = self.algorithm_combo.currentText()

        info_texts = {
            "传统算法": "基础算法，生成速度快，适合快速测试",
            "优化算法(推荐)": "平衡质量和效率，推荐日常使用",
            "原增强算法(极严格)": "最严格的重复检测，质量最高但数量较少",
            "测试算法(实验性)": "🧪 基于原增强算法改进，采用素材使用均衡策略，提高通过率稳定性"
        }

        info_text = info_texts.get(algorithm_text, "")
        self.algorithm_info_label.setText(info_text)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 关闭历史感知生成器的数据库连接
        if hasattr(self, 'history_aware_generator') and self.history_aware_generator:
            self.history_aware_generator.close()

        # 关闭历史统计窗口
        if hasattr(self, 'history_stats_window') and self.history_stats_window:
            self.history_stats_window.close()

        event.accept()
