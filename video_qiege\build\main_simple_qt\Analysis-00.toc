(['C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\main_simple_qt.py'],
 ['C:\\Users\\<USER>\\Desktop\\逆向重构\\appx'],
 [],
 [('D:\\Python\\Python39-32\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Python\\Python39-32\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Python\\Python39-32\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:24:45) [MSC v.1929 32 bit '
 '(Intel)]',
 [('pyi_rth_inspect',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main_simple_qt',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\main_simple_qt.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Python\\Python39-32\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\Python\\Python39-32\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Python\\Python39-32\\lib\\contextlib.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python\\Python39-32\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\Python39-32\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Python39-32\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Python39-32\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Python39-32\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Python39-32\\lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Python39-32\\lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\Python39-32\\lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\Python\\Python39-32\\lib\\bz2.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\Python39-32\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Python39-32\\lib\\calendar.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Python39-32\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Python\\Python39-32\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\Python\\Python39-32\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Python39-32\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Python39-32\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Python39-32\\lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Python39-32\\lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Python\\Python39-32\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Python\\Python39-32\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Python\\Python39-32\\lib\\typing.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python\\Python39-32\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Python\\Python39-32\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'D:\\Python\\Python39-32\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Python39-32\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Python39-32\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Python39-32\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser', 'D:\\Python\\Python39-32\\lib\\configparser.py', 'PYMODULE'),
  ('email', 'D:\\Python\\Python39-32\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Python\\Python39-32\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Python\\Python39-32\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils', 'D:\\Python\\Python39-32\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\Python\\Python39-32\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket', 'D:\\Python\\Python39-32\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Python39-32\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Python39-32\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Python39-32\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Python39-32\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Python39-32\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Python39-32\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Python39-32\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Python39-32\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Python39-32\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\Python39-32\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Python39-32\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Python39-32\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Python\\Python39-32\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\Python\\Python39-32\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Python39-32\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Python\\Python39-32\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Python\\Python39-32\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Python\\Python39-32\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Python39-32\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Python\\Python39-32\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Python39-32\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Python\\Python39-32\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Python\\Python39-32\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Python39-32\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('quopri', 'D:\\Python\\Python39-32\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Python39-32\\lib\\getopt.py', 'PYMODULE'),
  ('uu', 'D:\\Python\\Python39-32\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Python\\Python39-32\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Python39-32\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Python\\Python39-32\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Python\\Python39-32\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Python\\Python39-32\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Python\\Python39-32\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Python\\Python39-32\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\Python\\Python39-32\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\Python39-32\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Python39-32\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Python39-32\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('signal', 'D:\\Python\\Python39-32\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Python\\Python39-32\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\Python\\Python39-32\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Python\\Python39-32\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Python\\Python39-32\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Python\\Python39-32\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Python\\Python39-32\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Python\\Python39-32\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python\\Python39-32\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python\\Python39-32\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Python\\Python39-32\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\Python\\Python39-32\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Python\\Python39-32\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Python\\Python39-32\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\Python\\Python39-32\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Python\\Python39-32\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Python\\Python39-32\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Python\\Python39-32\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Python\\Python39-32\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\Python\\Python39-32\\lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\Python\\Python39-32\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\Python\\Python39-32\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\Python\\Python39-32\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Python\\Python39-32\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Python\\Python39-32\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Python\\Python39-32\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Python\\Python39-32\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Python\\Python39-32\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'D:\\Python\\Python39-32\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Python39-32\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Python\\Python39-32\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Python39-32\\lib\\ast.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python\\Python39-32\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Python39-32\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Python39-32\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Python39-32\\lib\\stringprep.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Python39-32\\lib\\tempfile.py', 'PYMODULE'),
  ('base64', 'D:\\Python\\Python39-32\\lib\\base64.py', 'PYMODULE'),
  ('json', 'D:\\Python\\Python39-32\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Python\\Python39-32\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Python\\Python39-32\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Python\\Python39-32\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('datetime', 'D:\\Python\\Python39-32\\lib\\datetime.py', 'PYMODULE'),
  ('video.face_detector',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\video\\face_detector.py',
   'PYMODULE'),
  ('video', '-', 'PYMODULE'),
  ('numpy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Python\\Python39-32\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Python\\Python39-32\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Python\\Python39-32\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Python39-32\\lib\\ipaddress.py', 'PYMODULE'),
  ('doctest', 'D:\\Python\\Python39-32\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Python\\Python39-32\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\Python\\Python39-32\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Python\\Python39-32\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'D:\\Python\\Python39-32\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\Python\\Python39-32\\lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\Python\\Python39-32\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Python\\Python39-32\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Python\\Python39-32\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Python\\Python39-32\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Python\\Python39-32\\lib\\tty.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Python39-32\\lib\\glob.py', 'PYMODULE'),
  ('code', 'D:\\Python\\Python39-32\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Python\\Python39-32\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Python\\Python39-32\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Python\\Python39-32\\lib\\cmd.py', 'PYMODULE'),
  ('__future__', 'D:\\Python\\Python39-32\\lib\\__future__.py', 'PYMODULE'),
  ('difflib', 'D:\\Python\\Python39-32\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.case',
   'D:\\Python\\Python39-32\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Python\\Python39-32\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Python\\Python39-32\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Python\\Python39-32\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('platform', 'D:\\Python\\Python39-32\\lib\\platform.py', 'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Python\\Python39-32\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Python\\Python39-32\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'D:\\Python\\Python39-32\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\Python\\Python39-32\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python\\Python39-32\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Python\\Python39-32\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Python\\Python39-32\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Python\\Python39-32\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Python\\Python39-32\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Python\\Python39-32\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python\\Python39-32\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Python\\Python39-32\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Python\\Python39-32\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Python\\Python39-32\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Python\\Python39-32\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Python\\Python39-32\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Python\\Python39-32\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Python\\Python39-32\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Python\\Python39-32\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Python\\Python39-32\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Python\\Python39-32\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Python\\Python39-32\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python\\Python39-32\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python\\Python39-32\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Python\\Python39-32\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Python\\Python39-32\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Python\\Python39-32\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Python\\Python39-32\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Python\\Python39-32\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Python\\Python39-32\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Python39-32\\lib\\dataclasses.py', 'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('video.video_processor',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\video\\video_processor.py',
   'PYMODULE'),
  ('utils.device_id',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\utils\\device_id.py',
   'PYMODULE'),
  ('utils', '-', 'PYMODULE'),
  ('auth.eydata_auth',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\auth\\eydata_auth.py',
   'PYMODULE'),
  ('auth', '-', 'PYMODULE'),
  ('requests',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Python\\Python39-32\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Python\\Python39-32\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('brotli',
   'D:\\Python\\Python39-32\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'D:\\Python\\Python39-32\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Python39-32\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Python\\Python39-32\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Python\\Python39-32\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('subprocess', 'D:\\Python\\Python39-32\\lib\\subprocess.py', 'PYMODULE')],
 [('python39.dll', 'D:\\Python\\Python39-32\\python39.dll', 'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\Python\\Python39-32\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python\\Python39-32\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Python\\Python39-32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Python\\Python39-32\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python\\Python39-32\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python\\Python39-32\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python\\Python39-32\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Python\\Python39-32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Python\\Python39-32\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Python\\Python39-32\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python\\Python39-32\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Python\\Python39-32\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp39-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Python\\Python39-32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Python\\Python39-32\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\mtrand.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_sfc64.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_philox.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_pcg64.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_mt19937.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\bit_generator.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_generator.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\random\\_common.cp39-win32.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp39-win32.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\yaml\\_yaml.cp39-win32.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\_cffi.cp39-win32.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\zstandard\\backend_c.cp39-win32.pyd',
   'EXTENSION'),
  ('_brotli.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\_brotli.cp39-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\charset_normalizer\\md.cp39-win32.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp39-win32.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\sip.cp39-win32.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Python\\Python39-32\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Python\\Python39-32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Python\\Python39-32\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\Python\\Python39-32\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\Python\\Python39-32\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python\\Python39-32\\python3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY')],
 [],
 [],
 [('cv2\\config-3.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Python\\Python39-32\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Python\\Python39-32\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('cv2\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\逆向重构\\appx\\build\\main_simple_qt\\base_library.zip',
   'DATA')],
 [('warnings', 'D:\\Python\\Python39-32\\lib\\warnings.py', 'PYMODULE'),
  ('functools', 'D:\\Python\\Python39-32\\lib\\functools.py', 'PYMODULE'),
  ('keyword', 'D:\\Python\\Python39-32\\lib\\keyword.py', 'PYMODULE'),
  ('reprlib', 'D:\\Python\\Python39-32\\lib\\reprlib.py', 'PYMODULE'),
  ('types', 'D:\\Python\\Python39-32\\lib\\types.py', 'PYMODULE'),
  ('_bootlocale', 'D:\\Python\\Python39-32\\lib\\_bootlocale.py', 'PYMODULE'),
  ('linecache', 'D:\\Python\\Python39-32\\lib\\linecache.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Python\\Python39-32\\lib\\sre_compile.py', 'PYMODULE'),
  ('abc', 'D:\\Python\\Python39-32\\lib\\abc.py', 'PYMODULE'),
  ('copyreg', 'D:\\Python\\Python39-32\\lib\\copyreg.py', 'PYMODULE'),
  ('stat', 'D:\\Python\\Python39-32\\lib\\stat.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Python\\Python39-32\\lib\\_weakrefset.py', 'PYMODULE'),
  ('ntpath', 'D:\\Python\\Python39-32\\lib\\ntpath.py', 'PYMODULE'),
  ('re', 'D:\\Python\\Python39-32\\lib\\re.py', 'PYMODULE'),
  ('codecs', 'D:\\Python\\Python39-32\\lib\\codecs.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Python\\Python39-32\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Python\\Python39-32\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Python\\Python39-32\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Python\\Python39-32\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Python\\Python39-32\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Python\\Python39-32\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Python\\Python39-32\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Python\\Python39-32\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Python\\Python39-32\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Python\\Python39-32\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Python\\Python39-32\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Python\\Python39-32\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Python\\Python39-32\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Python\\Python39-32\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Python\\Python39-32\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Python\\Python39-32\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Python\\Python39-32\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Python\\Python39-32\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Python\\Python39-32\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Python\\Python39-32\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Python\\Python39-32\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Python\\Python39-32\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Python\\Python39-32\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Python\\Python39-32\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Python\\Python39-32\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Python\\Python39-32\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Python\\Python39-32\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Python\\Python39-32\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Python\\Python39-32\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Python\\Python39-32\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Python\\Python39-32\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Python\\Python39-32\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Python\\Python39-32\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Python\\Python39-32\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Python\\Python39-32\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Python\\Python39-32\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Python\\Python39-32\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Python\\Python39-32\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Python\\Python39-32\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Python\\Python39-32\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('locale', 'D:\\Python\\Python39-32\\lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\Python\\Python39-32\\lib\\operator.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\Python\\Python39-32\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Python\\Python39-32\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('sre_parse', 'D:\\Python\\Python39-32\\lib\\sre_parse.py', 'PYMODULE'),
  ('io', 'D:\\Python\\Python39-32\\lib\\io.py', 'PYMODULE'),
  ('genericpath', 'D:\\Python\\Python39-32\\lib\\genericpath.py', 'PYMODULE'),
  ('heapq', 'D:\\Python\\Python39-32\\lib\\heapq.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Python\\Python39-32\\lib\\sre_constants.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\Python\\Python39-32\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('enum', 'D:\\Python\\Python39-32\\lib\\enum.py', 'PYMODULE'),
  ('posixpath', 'D:\\Python\\Python39-32\\lib\\posixpath.py', 'PYMODULE'),
  ('traceback', 'D:\\Python\\Python39-32\\lib\\traceback.py', 'PYMODULE'),
  ('weakref', 'D:\\Python\\Python39-32\\lib\\weakref.py', 'PYMODULE'),
  ('cv2',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\__init__.py',
   'PYMODULE'),
  ('cv2.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\version.py',
   'PYMODULE'),
  ('cv2.utils',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'PYMODULE'),
  ('cv2.typing',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'PYMODULE'),
  ('cv2.gapi.wip.draw', '-', 'PYMODULE'),
  ('cv2.gapi.wip', '-', 'PYMODULE'),
  ('cv2.dnn', '-', 'PYMODULE'),
  ('cv2.misc.version',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\misc\\version.py',
   'PYMODULE'),
  ('cv2.misc',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'PYMODULE'),
  ('cv2.mat_wrapper',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'PYMODULE'),
  ('cv2.gapi',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'PYMODULE'),
  ('cv2.data',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\data\\__init__.py',
   'PYMODULE'),
  ('cv2.config-3',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\config-3.py',
   'PYMODULE'),
  ('cv2.config',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\config.py',
   'PYMODULE'),
  ('cv2.load_config_py3',
   'D:\\Python\\Python39-32\\lib\\site-packages\\cv2\\load_config_py3.py',
   'PYMODULE'),
  ('os', 'D:\\Python\\Python39-32\\lib\\os.py', 'PYMODULE')])
