#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频混剪工具 - 主程序入口
Video Mixer Tool - Main Entry Point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.main_window import MainWindow
    from utils.config import Config
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from gui.main_window import MainWindow
    from utils.config import Config

def main():
    """主程序入口"""
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("视频混剪工具-裕页")

    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 初始化日志系统
    from utils.logger import init_logger
    logger = init_logger()
    logger.info("🚀 应用程序启动")

    # 加载配置
    config = Config()

    # 创建主窗口
    try:
        main_window = MainWindow(config)
        main_window.show()

        # 运行应用程序
        sys.exit(app.exec_())
    except RuntimeError as e:
        # 用户取消登录，直接退出
        print(f"程序退出: {e}")
        sys.exit(0)

if __name__ == "__main__":
    main()
