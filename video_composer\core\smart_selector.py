"""
智能片段选择器
"""
from typing import List, Dict
import random

class SmartSelector:
    """智能片段选择算法"""
    
    def __init__(self):
        self.last_source = None
        self.source_usage = {}
    
    def select_segments(self, all_segments: List[Dict], target_duration: float) -> List[Dict]:
        """
        智能选择片段，保持逻辑性
        
        Args:
            all_segments: 所有可用片段
            target_duration: 目标总时长
            
        Returns:
            选中的片段列表
        """
        # 按来源分组
        segments_by_source = {}
        for segment in all_segments:
            source = segment['source']
            if source not in segments_by_source:
                segments_by_source[source] = []
            segments_by_source[source].append(segment)
        
        selected = []
        current_duration = 0.0
        sources = list(segments_by_source.keys())
        
        # 初始化使用计数
        for source in sources:
            self.source_usage[source] = 0
        
        while current_duration < target_duration and any(segments_by_source.values()):
            # 选择下一个来源（避免连续使用同一来源）
            available_sources = [s for s in sources if segments_by_source[s] and s != self.last_source]
            
            if not available_sources:
                available_sources = [s for s in sources if segments_by_source[s]]
            
            if not available_sources:
                break
            
            # 优先选择使用次数较少的来源
            next_source = min(available_sources, key=lambda s: self.source_usage[s])
            
            # 从该来源选择片段
            segment = segments_by_source[next_source].pop(0)
            selected.append(segment)
            
            current_duration += segment['duration']
            self.last_source = next_source
            self.source_usage[next_source] += 1
        
        return selected