"""
智能片段选择器
"""
from typing import List, Dict
import random

class SmartSelector:
    """智能片段选择算法"""
    
    def __init__(self):
        self.last_source = None
        self.source_usage = {}
    
    def select_segments(self, all_segments: List[Dict], target_duration: float) -> List[Dict]:
        """
        智能选择片段，随机来源但全局序号递增

        Args:
            all_segments: 所有可用片段
            target_duration: 目标总时长

        Returns:
            选中的片段列表
        """
        import random

        # 按来源分组并排序
        segments_by_source = {}
        for segment in all_segments:
            source = segment['source']
            if source not in segments_by_source:
                segments_by_source[source] = []
            segments_by_source[source].append(segment)

        # 按来源名称排序，确保顺序一致 (A, B, C)
        sources = sorted(segments_by_source.keys())
        print(f"视频来源: {sources}")

        # 为每个来源的片段按时间顺序排序（保证内部序号规则）
        for source in sources:
            segments_by_source[source].sort(key=lambda x: x['start_time'])
            print(f"来源 {source}: {len(segments_by_source[source])} 个片段")

        # 记录每个来源当前使用到的片段索引
        source_current_index = {source: 0 for source in sources}

        selected = []
        current_duration = 0.0
        global_index = 1  # 全局序号

        # 随机选择来源，但全局序号递增
        while current_duration < target_duration:
            # 检查是否还有可用片段
            available_sources = []
            for source in sources:
                if source_current_index[source] < len(segments_by_source[source]):
                    available_sources.append(source)

            if not available_sources:
                print("所有片段已用完")
                break

            # 随机选择一个有片段的来源
            current_source = random.choice(available_sources)

            # 从该来源选择下一个片段
            segment_idx = source_current_index[current_source]
            segment = segments_by_source[current_source][segment_idx]

            # 创建新的片段信息，使用全局序号
            new_segment = segment.copy()
            new_segment['global_index'] = global_index
            new_segment['original_index'] = segment['index']

            selected.append(new_segment)
            current_duration += segment['duration']

            print(f"选择片段: {segment['source']}-{global_index} "
                  f"(原序号: {segment['index']}, 时长: {segment['duration']:.2f}s, 累计: {current_duration:.2f}s)")

            # 更新索引
            source_current_index[current_source] += 1
            global_index += 1

            # 如果达到目标时长，停止选择
            if current_duration >= target_duration:
                break

        print(f"总共选择了 {len(selected)} 个片段，总时长: {current_duration:.2f}s")
        return selected