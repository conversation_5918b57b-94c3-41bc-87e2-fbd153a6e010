"""
智能片段选择器
"""
from typing import List, Dict
import random

class SmartSelector:
    """智能片段选择算法"""
    
    def __init__(self):
        self.last_source = None
        self.source_usage = {}
    
    def select_segments(self, all_segments: List[Dict], target_duration: float) -> List[Dict]:
        """
        智能选择片段，按轮换顺序选择

        Args:
            all_segments: 所有可用片段
            target_duration: 目标总时长

        Returns:
            选中的片段列表
        """
        # 按来源分组并排序
        segments_by_source = {}
        for segment in all_segments:
            source = segment['source']
            if source not in segments_by_source:
                segments_by_source[source] = []
            segments_by_source[source].append(segment)

        # 按来源名称排序，确保顺序一致 (A, B, C)
        sources = sorted(segments_by_source.keys())
        print(f"视频来源: {sources}")

        # 为每个来源的片段按时间顺序排序
        for source in sources:
            segments_by_source[source].sort(key=lambda x: x['start_time'])
            print(f"来源 {source}: {len(segments_by_source[source])} 个片段")

        selected = []
        current_duration = 0.0
        source_index = 0  # 当前选择的来源索引

        # 轮换选择片段
        while current_duration < target_duration:
            # 检查是否还有可用片段
            available_sources = [s for s in sources if segments_by_source[s]]
            if not available_sources:
                print("所有片段已用完")
                break

            # 找到下一个有片段的来源
            attempts = 0
            while attempts < len(sources):
                current_source = sources[source_index % len(sources)]

                if segments_by_source[current_source]:
                    # 选择该来源的下一个片段
                    segment = segments_by_source[current_source].pop(0)
                    selected.append(segment)
                    current_duration += segment['duration']

                    print(f"选择片段: {segment['source']}-{segment['index']} "
                          f"(时长: {segment['duration']:.2f}s, 累计: {current_duration:.2f}s)")

                    # 移动到下一个来源
                    source_index += 1
                    break
                else:
                    # 当前来源没有片段，尝试下一个
                    source_index += 1
                    attempts += 1

            if attempts >= len(sources):
                # 所有来源都没有片段了
                break

        print(f"总共选择了 {len(selected)} 个片段，总时长: {current_duration:.2f}s")
        return selected