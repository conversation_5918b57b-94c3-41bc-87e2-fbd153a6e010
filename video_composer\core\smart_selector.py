"""
智能片段选择器
"""
from typing import List, Dict
import random

class SmartSelector:
    """智能片段选择算法"""
    
    def __init__(self):
        self.last_source = None
        self.source_usage = {}
    
    def select_segments(self, all_segments: List[Dict], target_duration: float) -> List[Dict]:
        """
        智能选择片段：随机来源 + 全局序号递增 + 智能回退

        Args:
            all_segments: 所有可用片段
            target_duration: 目标总时长

        Returns:
            选中的片段列表
        """
        import random

        # 按来源分组并排序
        segments_by_source = {}
        for segment in all_segments:
            source = segment['source']
            if source not in segments_by_source:
                segments_by_source[source] = []
            segments_by_source[source].append(segment)

        # 按来源名称排序，确保顺序一致 (A, B, C)
        sources = sorted(segments_by_source.keys())
        print(f"视频来源: {sources}")

        # 为每个来源的片段按时间顺序排序
        for source in sources:
            segments_by_source[source].sort(key=lambda x: x['start_time'])
            print(f"来源 {source}: {len(segments_by_source[source])} 个片段")

        # 创建所有片段的可用列表（用于智能回退）
        all_available = []
        for source in sources:
            for i, segment in enumerate(segments_by_source[source]):
                all_available.append({
                    'segment': segment,
                    'source': source,
                    'source_index': i,
                    'used': False
                })

        selected = []
        current_duration = 0.0
        global_index = 1  # 全局序号

        while current_duration < target_duration:
            # 检查是否还有可用片段
            available_segments = [item for item in all_available if not item['used']]
            if not available_segments:
                print("所有片段已用完")
                break

            # 随机选择一个来源
            preferred_source = random.choice(sources)

            # 尝试从首选来源找到下一个未使用的片段
            found_segment = None
            for item in available_segments:
                if item['source'] == preferred_source:
                    found_segment = item
                    break

            # 如果首选来源没有可用片段，从其他来源选择
            if found_segment is None:
                found_segment = available_segments[0]  # 选择第一个可用的
                print(f"来源 {preferred_source} 无可用片段，改选 {found_segment['source']}")

            # 标记为已使用
            found_segment['used'] = True
            segment = found_segment['segment']

            # 创建新的片段信息，使用全局序号
            new_segment = segment.copy()
            new_segment['display_name'] = f"{segment['source']}-{global_index}"
            new_segment['original_name'] = f"{segment['source']}-{segment['index']}"

            selected.append(new_segment)
            current_duration += segment['duration']

            print(f"选择片段: {new_segment['display_name']} "
                  f"(原名: {new_segment['original_name']}, 时长: {segment['duration']:.2f}s, 累计: {current_duration:.2f}s)")

            global_index += 1

            # 如果达到目标时长，停止选择
            if current_duration >= target_duration:
                break

        print(f"总共选择了 {len(selected)} 个片段，总时长: {current_duration:.2f}s")
        return selected