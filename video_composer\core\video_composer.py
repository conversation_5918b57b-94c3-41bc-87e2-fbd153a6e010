"""
视频合成器 - 提取自video_mixer项目
"""
import os
import subprocess
import tempfile
from typing import List, Dict

class VideoComposer:
    """精简版视频合成器"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg"):
        self.ffmpeg_path = ffmpeg_path
    
    def extract_audio(self, video_path: str, output_path: str) -> bool:
        """提取视频音频"""
        cmd = [
            self.ffmpeg_path, "-i", video_path,
            "-vn", "-acodec", "copy", "-y", output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True,
                              creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
        return result.returncode == 0
    
    def merge_segments(self, segments: List[str], audio_path: str, 
                      subtitle_path: str, output_path: str) -> bool:
        """合并视频片段"""
        # 创建文件列表
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            for segment in segments:
                f.write(f"file '{segment}'\n")
            list_file = f.name
        
        try:
            # 先合并视频
            temp_video = tempfile.mktemp(suffix='.mp4')
            cmd = [
                self.ffmpeg_path, "-f", "concat", "-safe", "0",
                "-i", list_file, "-c", "copy", "-y", temp_video
            ]
            
            result = subprocess.run(cmd, capture_output=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
            
            if result.returncode != 0:
                return False
            
            # 添加音频和字幕
            cmd = [
                self.ffmpeg_path,
                "-i", temp_video,
                "-i", audio_path,
                "-vf", f"subtitles={subtitle_path}",
                "-c:v", "libx264", "-c:a", "aac",
                "-shortest", "-y", output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True,
                                  creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
            
            return result.returncode == 0
            
        finally:
            # 清理临时文件
            try:
                os.unlink(list_file)
                if os.path.exists(temp_video):
                    os.unlink(temp_video)
            except:
                pass