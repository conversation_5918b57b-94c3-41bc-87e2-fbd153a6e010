"""
视频合成器 - 提取自video_mixer项目
"""
import os
import subprocess
import tempfile
from typing import List, Dict

class VideoComposer:
    """精简版视频合成器"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg"):
        self.ffmpeg_path = ffmpeg_path
    
    def extract_audio(self, video_path: str, output_path: str) -> bool:
        """提取视频音频"""
        cmd = [
            self.ffmpeg_path, "-i", video_path,
            "-vn", "-acodec", "copy", "-y", output_path
        ]

        kwargs = {
            'capture_output': True,
            'text': True,
            'encoding': 'utf-8',
            'errors': 'ignore',
            'timeout': 300
        }

        # 在Windows下隐藏命令窗口
        if os.name == 'nt':
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

        try:
            result = subprocess.run(cmd, **kwargs)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, UnicodeDecodeError) as e:
            print(f"提取音频失败: {str(e)}")
            return False
    
    def merge_segments(self, segments: List[str], audio_source_video: str,
                      subtitle_path: str, output_path: str, skip_subtitle: bool = False) -> bool:
        """合并视频片段"""
        # 创建文件列表
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            for segment in segments:
                # 检查文件是否存在
                if not os.path.exists(segment):
                    print(f"警告: 片段文件不存在: {segment}")
                    continue

                # 使用正斜杠路径，避免Windows路径问题
                segment_path = segment.replace('\\', '/')
                f.write(f"file '{segment_path}'\n")
                print(f"添加片段到列表: {segment_path}")
            list_file = f.name

        print(f"文件列表创建完成: {list_file}")

        temp_video = None
        temp_subtitle = None

        try:
            # 先合并视频
            temp_video = tempfile.mktemp(suffix='.mp4')
            cmd = [
                self.ffmpeg_path, "-f", "concat", "-safe", "0",
                "-i", list_file, "-c", "copy", "-y", temp_video
            ]

            kwargs = {
                'capture_output': True,
                'text': True,
                'encoding': 'utf-8',
                'errors': 'ignore',
                'timeout': 600
            }

            # 在Windows下隐藏命令窗口
            if os.name == 'nt':
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

            result = subprocess.run(cmd, **kwargs)

            if result.returncode != 0:
                print(f"合并视频失败: {result.stderr}")
                return False

            # 使用最短视频的音频，合并视频片段
            print(f"使用音频源视频: {audio_source_video}")
            print(f"合并的视频片段: {temp_video}")

            # 直接合并：片段视频 + 最短视频的音频
            cmd = [
                self.ffmpeg_path,
                "-i", temp_video,           # 合并后的视频片段（无音频）
                "-i", audio_source_video,   # 最短视频（作为音频源）
                "-map", "0:v",              # 使用第一个输入的视频流
                "-map", "1:a",              # 使用第二个输入的音频流
                "-c:v", "libx264",
                "-c:a", "aac",
                "-shortest",                # 以最短流为准
                "-y", output_path
            ]

            print(f"FFmpeg命令: {' '.join(cmd)}")

            # 如果需要字幕，可以在这里添加字幕处理逻辑
            if not skip_subtitle and subtitle_path and os.path.exists(subtitle_path):
                print("注意: 当前版本暂时不支持字幕烧录，将在后续版本中添加")

            result = subprocess.run(cmd, **kwargs)

            if result.returncode == 0:
                print("视频合成成功")
                return True
            else:
                print(f"视频合成失败: {result.stderr}")
                return False

        except (subprocess.TimeoutExpired, UnicodeDecodeError) as e:
            print(f"合成过程出错: {str(e)}")
            return False

        finally:
            # 清理临时文件
            try:
                if os.path.exists(list_file):
                    os.unlink(list_file)
                if temp_video and os.path.exists(temp_video):
                    os.unlink(temp_video)
                # 清理临时字幕文件
                if temp_subtitle and os.path.exists(temp_subtitle):
                    os.unlink(temp_subtitle)
            except Exception as e:
                print(f"清理临时文件失败: {str(e)}")