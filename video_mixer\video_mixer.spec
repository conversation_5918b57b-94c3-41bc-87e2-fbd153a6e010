# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 添加项目路径
sys.path.insert(0, project_root)

# 定义隐藏导入
hiddenimports = [
    # 项目内部模块
    'gui',
    'gui.main_window',
    'gui.login_dialog', 
    'gui.video_generation_thread',
    'gui.history_stats_widget',
    'core',
    'core.subtitle_parser',
    'core.video_processor',
    'core.algorithm',
    'core.auth_manager',
    'core.database',
    'core.history_aware_generator',
    'core.original_enhanced_algorithm',
    'core.video_history_database',
    'utils',
    'utils.config',
    'utils.device_id',
    
    # 第三方库的隐藏导入
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'PyQt5.QtMultimediaWidgets',
    'numpy',
    'numpy.core',
    'numpy.core._methods',
    'numpy.lib.format',
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    'urllib3',
    'urllib3.util',
    'urllib3.util.retry',
    'urllib3.util.connection',
    'urllib3.connection',
    'urllib3.connectionpool',
    'urllib3.poolmanager',
    'urllib3.response',
    'certifi',
    'charset_normalizer',
    'idna',
    'pysrt',
    'ffmpeg',
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'psutil',
    'sqlite3',
    'json',
    'base64',
    'hashlib',
    'platform',
    'subprocess',
    're',
    'os',
    'sys',
    'threading',
    'multiprocessing',
    'queue',
    'time',
    'datetime',
    'random',
    'collections',
    'dataclasses',
    'typing',
]

# 数据文件
datas = [
    # 包含图标文件
    ('1.ico', '.'),
    # 如果有其他资源文件，在这里添加
]

# 二进制文件（如果需要包含FFmpeg等）
binaries = []

# 排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
    'notebook',
]

a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='视频混剪工具-裕页',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='1.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='视频混剪工具-裕页',
)
