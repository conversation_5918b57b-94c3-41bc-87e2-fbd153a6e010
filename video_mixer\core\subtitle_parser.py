#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕解析模块
Subtitle Parser Module
"""

import re
import os
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class SubtitleItem:
    """字幕项数据类"""
    id: int
    start_time: str  # 格式: "00:00:01,000"
    end_time: str    # 格式: "00:00:02,500"
    text: str
    start_ms: int    # 开始时间（毫秒）
    end_ms: int      # 结束时间（毫秒）
    duration_ms: int # 持续时间（毫秒）


class SubtitleParser:
    """字幕解析器"""
    
    @staticmethod
    def time_to_milliseconds(time_str: str) -> int:
        """将时间字符串转换为毫秒"""
        try:
            # 匹配格式: HH:MM:SS,mmm
            pattern = r'(\d+):(\d+):(\d+),(\d+)'
            match = re.match(pattern, time_str)
            
            if not match:
                raise ValueError(f"无效的时间格式: {time_str}")
            
            hours = int(match.group(1))
            minutes = int(match.group(2))
            seconds = int(match.group(3))
            milliseconds = int(match.group(4))
            
            total_ms = (
                hours * 3600 * 1000 +
                minutes * 60 * 1000 +
                seconds * 1000 +
                milliseconds
            )
            
            return total_ms
            
        except Exception as e:
            print(f"时间转换失败: {e}")
            return 0
    
    @staticmethod
    def milliseconds_to_time(ms: int) -> str:
        """将毫秒转换为时间字符串"""
        try:
            hours = ms // (3600 * 1000)
            minutes = (ms % (3600 * 1000)) // (60 * 1000)
            seconds = (ms % (60 * 1000)) // 1000
            milliseconds = ms % 1000
            
            return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
            
        except Exception as e:
            print(f"时间格式化失败: {e}")
            return "00:00:00,000"
    
    @classmethod
    def parse_srt_file(cls, file_path: str) -> List[SubtitleItem]:
        """解析SRT字幕文件"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"字幕文件不存在: {file_path}")
            
            # 尝试不同的编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            content = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise ValueError("无法读取字幕文件，编码格式不支持")
            
            return cls.parse_srt_content(content)
            
        except Exception as e:
            print(f"解析SRT文件失败: {e}")
            return []
    
    @classmethod
    def parse_srt_content(cls, content: str) -> List[SubtitleItem]:
        """解析SRT字幕内容"""
        try:
            subtitles = []
            
            # 分割字幕块
            blocks = re.split(r'\n\s*\n', content.strip())
            
            for block in blocks:
                if not block.strip():
                    continue
                
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                try:
                    # 解析序号
                    subtitle_id = int(lines[0].strip())
                    
                    # 解析时间轴
                    time_line = lines[1].strip()
                    time_match = re.match(r'(\d+:\d+:\d+,\d+)\s*-->\s*(\d+:\d+:\d+,\d+)', time_line)
                    
                    if not time_match:
                        print(f"无效的时间轴格式: {time_line}")
                        continue
                    
                    start_time = time_match.group(1)
                    end_time = time_match.group(2)
                    
                    # 解析文本内容
                    text_lines = lines[2:]
                    text = '\n'.join(text_lines).strip()
                    
                    # 计算时间（毫秒）
                    start_ms = cls.time_to_milliseconds(start_time)
                    end_ms = cls.time_to_milliseconds(end_time)
                    duration_ms = end_ms - start_ms
                    
                    if duration_ms <= 0:
                        print(f"无效的字幕时长: {start_time} -> {end_time}")
                        continue
                    
                    # 创建字幕项
                    subtitle_item = SubtitleItem(
                        id=subtitle_id,
                        start_time=start_time,
                        end_time=end_time,
                        text=text,
                        start_ms=start_ms,
                        end_ms=end_ms,
                        duration_ms=duration_ms
                    )
                    
                    subtitles.append(subtitle_item)
                    
                except Exception as e:
                    print(f"解析字幕块失败: {e}, 内容: {block}")
                    continue
            
            # 按ID排序
            subtitles.sort(key=lambda x: x.id)
            
            print(f"成功解析 {len(subtitles)} 条字幕")
            return subtitles
            
        except Exception as e:
            print(f"解析SRT内容失败: {e}")
            return []
    
    @staticmethod
    def get_subtitle_stats(subtitles: List[SubtitleItem]) -> Dict[str, Any]:
        """获取字幕统计信息"""
        if not subtitles:
            return {
                "count": 0,
                "total_duration_ms": 0,
                "min_duration_ms": 0,
                "max_duration_ms": 0,
                "avg_duration_ms": 0
            }
        
        durations = [sub.duration_ms for sub in subtitles]
        
        return {
            "count": len(subtitles),
            "total_duration_ms": sum(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "avg_duration_ms": sum(durations) // len(durations),
            "first_start_ms": subtitles[0].start_ms,
            "last_end_ms": subtitles[-1].end_ms
        }
    
    @staticmethod
    def validate_subtitles(subtitles: List[SubtitleItem]) -> List[str]:
        """验证字幕数据，返回错误信息列表"""
        errors = []
        
        if not subtitles:
            errors.append("字幕列表为空")
            return errors
        
        for i, subtitle in enumerate(subtitles):
            # 检查时间有效性
            if subtitle.duration_ms <= 0:
                errors.append(f"第{subtitle.id}条字幕时长无效: {subtitle.duration_ms}ms")
            
            # 检查文本内容
            if not subtitle.text.strip():
                errors.append(f"第{subtitle.id}条字幕文本为空")
            
            # 检查时间重叠（与下一条字幕）
            if i < len(subtitles) - 1:
                next_subtitle = subtitles[i + 1]
                if subtitle.end_ms > next_subtitle.start_ms:
                    errors.append(f"第{subtitle.id}条和第{next_subtitle.id}条字幕时间重叠")
        
        return errors


# 测试代码
if __name__ == "__main__":
    # 创建测试SRT内容
    test_srt_content = """1
00:00:01,000 --> 00:00:03,500
这是第一条字幕

2
00:00:04,000 --> 00:00:06,000
这是第二条字幕

3
00:00:07,000 --> 00:00:09,500
这是第三条字幕
"""
    
    parser = SubtitleParser()
    subtitles = parser.parse_srt_content(test_srt_content)
    
    print(f"解析结果: {len(subtitles)} 条字幕")
    for sub in subtitles:
        print(f"ID: {sub.id}, 时长: {sub.duration_ms}ms, 文本: {sub.text}")
    
    # 获取统计信息
    stats = parser.get_subtitle_stats(subtitles)
    print(f"统计信息: {stats}")
    
    # 验证字幕
    errors = parser.validate_subtitles(subtitles)
    if errors:
        print(f"验证错误: {errors}")
    else:
        print("字幕验证通过")
