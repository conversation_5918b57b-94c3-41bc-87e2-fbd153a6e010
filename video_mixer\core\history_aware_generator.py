#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史感知视频生成器
基于历史记录避免重复组合，提升生成数量和质量
"""

import random
import numpy as np
import uuid
import os
import sys
from datetime import datetime
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass
from .video_history_database import VideoHistoryDB, CombinationSequence
from .algorithm import OptimizedVideoGenerator, GeneratedVideo, VideoSegment

try:
    from ..utils.logger import get_logger
except ImportError:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger

@dataclass
class MaterialWithMD5:
    """带MD5的素材信息"""
    index: int
    path: str
    md5: str
    duration: float

class HistoryAwareVideoGenerator(OptimizedVideoGenerator):
    """历史感知的视频生成器"""
    
    def __init__(self, num_materials: int = 100, segments_per_video: int = 4,
                 enable_similarity_check: bool = False, optimization_mode: str = "balanced"):
        super().__init__(num_materials, segments_per_video, enable_similarity_check, optimization_mode)

        # 数据库连接
        self.db = VideoHistoryDB()
        self.logger = get_logger()
        self.logger.info("🧠 历史感知视频生成器初始化完成")
        if not self.db.connect():
            print("❌ 历史感知生成器：数据库连接失败")
        else:
            print("✅ 历史感知生成器：数据库连接成功")
        
        # 素材MD5映射
        self.material_md5_map = {}  # {index: md5}
        self.md5_to_index_map = {}  # {md5: index}
        
        # 当前批次ID
        self.current_batch_id = self._generate_batch_id()
        
        print(f"🔄 历史感知生成器初始化完成，批次ID: {self.current_batch_id}")
    
    def _generate_batch_id(self) -> str:
        """生成批次ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"batch_{timestamp}_{str(uuid.uuid4())[:8]}"
    
    def set_material_paths(self, material_paths: List[str]):
        """设置素材路径并计算MD5"""
        print("🔍 正在计算素材MD5...")
        
        self.material_paths = material_paths
        self.material_md5_map = {}
        self.md5_to_index_map = {}
        
        for index, path in enumerate(material_paths):
            md5_hash = self.db.record_material(path)
            if md5_hash:
                self.material_md5_map[index] = md5_hash
                self.md5_to_index_map[md5_hash] = index
                print(f"✅ 素材{index}: {md5_hash[:8]}... - {path}")
            else:
                print(f"❌ 素材{index}: MD5计算失败 - {path}")
        
        print(f"✅ 完成MD5计算，共处理 {len(self.material_md5_map)} 个素材")
    
    def is_combination_used_in_history(self, video_segments: List[VideoSegment]) -> bool:
        """检查组合是否在历史中使用过"""
        if not self.material_md5_map:
            print("⚠️ 素材MD5映射为空，跳过历史检查")
            return False

        # 提取素材索引和位置
        material_indices = []
        positions = []

        for segment in video_segments:
            if segment.video_index in self.material_md5_map:
                material_indices.append(segment.video_index)
                # 使用start_time作为位置信息，而不是position索引
                positions.append(segment.start_time)
            else:
                # 如果有素材没有MD5，认为是新组合
                print(f"⚠️ 素材索引{segment.video_index}没有MD5映射，认为是新组合")
                return False

        # 创建组合序列对象
        try:
            combination_sequence = CombinationSequence(
                material_indices=material_indices,
                positions=positions
            )

            # 检查数据库
            is_used = self.db.is_combination_used(combination_sequence)
            if is_used:
                print(f"🔍 历史检查：组合已存在，跳过 - 哈希: {combination_sequence.get_hash()}")
            return is_used

        except Exception as e:
            print(f"❌ 历史检查失败: {e}")
            return False
    
    def record_generated_video(self, video: GeneratedVideo, quality_score: float = 0.0):
        """记录生成的视频到数据库"""
        if not self.material_md5_map:
            return
        
        material_md5s = []
        positions = []
        
        for segment in video.segments:
            if segment.video_index in self.material_md5_map:
                material_md5s.append(self.material_md5_map[segment.video_index])
                positions.append(segment.position)
        
        if len(material_md5s) == len(video.segments):
            success = self.db.record_combination(
                material_md5s, positions, self.current_batch_id, quality_score
            )
            if success:
                print(f"✅ 记录组合到数据库: {len(material_md5s)}个素材")
    
    def generate_optimal_videos_with_history(self, max_videos: int = 200) -> Tuple[int, List[GeneratedVideo]]:
        """基于历史记录生成视频"""
        print(f"🚀 开始历史感知生成，目标: {max_videos}个视频")

        # 设置随机种子确保一致性
        random.seed(42)

        # 首先使用父类的优化算法生成候选视频
        print("📊 使用优化算法生成候选视频...")
        candidate_count, candidate_videos = super().generate_optimal_videos(max_videos * 3)  # 生成更多候选

        print(f"✅ 优化算法生成了 {candidate_count} 个候选视频")

        # 然后基于历史记录过滤
        filtered_videos = []
        history_skipped = 0

        for candidate_video in candidate_videos:
            # 检查历史记录
            if self.is_combination_used_in_history(candidate_video.segments):
                history_skipped += 1
                continue

            # 通过历史检查，添加到结果（但不记录到数据库）
            filtered_videos.append(candidate_video)

            # 达到目标数量就停止
            if len(filtered_videos) >= max_videos:
                break

        print(f"🎉 历史感知计算完成！")
        print(f"📊 计算统计:")
        print(f"   - 候选视频: {candidate_count} 个")
        print(f"   - 跳过历史重复: {history_skipped} 个")
        print(f"   - 可生成视频: {len(filtered_videos)} 个")
        print(f"   - 历史避重率: {history_skipped/candidate_count*100:.1f}%" if candidate_count > 0 else "")
        print(f"   ⚠️ 注意：实际生成成功后才会记录到数据库")

        return len(filtered_videos), filtered_videos

    def record_successful_generation(self, generated_videos: List[GeneratedVideo]) -> bool:
        """记录成功生成的视频到数据库"""
        try:
            if not generated_videos:
                print("⚠️ 没有视频需要记录到数据库")
                return True

            print(f"📝 记录 {len(generated_videos)} 个成功生成的视频到数据库...")

            success_count = 0
            for i, video in enumerate(generated_videos):
                quality_score = 1.0 - (i / len(generated_videos)) * 0.3
                if self.record_generated_video(video, quality_score):
                    success_count += 1

            # 只有成功记录了视频才创建批次记录
            if success_count > 0:
                self.db.create_batch_record(
                    self.current_batch_id,
                    self.num_materials,
                    success_count,
                    self.optimization_mode
                )
                print(f"✅ 成功记录 {success_count}/{len(generated_videos)} 个视频到数据库")
            else:
                print("⚠️ 没有视频成功记录，不创建批次记录")

            return success_count == len(generated_videos)

        except Exception as e:
            print(f"❌ 记录生成结果失败: {e}")
            return False

    def get_history_stats(self) -> dict:
        """获取历史统计信息"""
        stats = self.db.get_generation_stats()
        usage_ranking = self.db.get_material_usage_ranking(20)
        
        return {
            'total_videos': stats.total_videos,
            'total_materials': stats.total_materials,
            'total_batches': stats.total_batches,
            'current_batch_id': self.current_batch_id,
            'top_used_materials': usage_ranking
        }
    
    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()
