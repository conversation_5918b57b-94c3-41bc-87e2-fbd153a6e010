#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成历史数据库管理
基于MySQL实现素材MD5管理和组合历史记录
"""

import mysql.connector
import hashlib
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

@dataclass
class MaterialInfo:
    """素材信息"""
    md5_hash: str
    file_name: str
    file_path: str
    file_size: int
    duration: float
    first_seen: datetime
    last_seen: datetime

@dataclass
class CombinationRecord:
    """组合记录"""
    id: int
    combination_hash: str
    material_md5s: List[str]
    positions: List[int]
    generation_time: datetime
    batch_id: str
    quality_score: float

@dataclass
class UsageStats:
    """使用统计"""
    md5_hash: str
    total_usage_count: int
    position_usage: Dict[int, int]
    last_used_time: datetime
    first_used_time: datetime

@dataclass
class GenerationStats:
    """生成统计"""
    total_videos: int
    total_materials: int
    total_batches: int
    total_combinations: int

class VideoGenerationDB:
    """视频生成历史数据库"""

    def __init__(self, host='*************', user='root', password='root', database='video_mixed'):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        self.is_connected = False
        try:
            self.connect()
            self.create_tables()
            self.is_connected = True
        except Exception as e:
            print(f"⚠️ 数据库初始化失败，将跳过数据库功能: {e}")
            self.is_connected = False
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4'
            )
            print(f"✅ 成功连接到MySQL数据库: {self.database}")
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            # 尝试创建数据库
            self._create_database_if_not_exists()
    
    def _create_database_if_not_exists(self):
        """如果数据库不存在则创建"""
        try:
            temp_conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                charset='utf8mb4'
            )
            cursor = temp_conn.cursor()
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            temp_conn.commit()
            cursor.close()
            temp_conn.close()
            
            # 重新连接到新创建的数据库
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4'
            )
            print(f"✅ 创建并连接到数据库: {self.database}")
        except mysql.connector.Error as e:
            print(f"❌ 创建数据库失败: {e}")
            raise
    
    def create_tables(self):
        """创建所需的表"""
        if not self.connection:
            print("⚠️ 数据库连接不存在，无法创建表")
            return

        try:
            cursor = self.connection.cursor()

            # 素材信息表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS materials (
                md5_hash VARCHAR(32) PRIMARY KEY,
                file_name VARCHAR(255) NOT NULL,
                file_path TEXT NOT NULL,
                file_size BIGINT NOT NULL,
                duration DECIMAL(10,3) NOT NULL,
                first_seen DATETIME NOT NULL,
                last_seen DATETIME NOT NULL,
                INDEX idx_file_path (file_path(255)),
                INDEX idx_last_seen (last_seen)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            print("✅ materials表创建完成")

            # 视频组合记录表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS video_combinations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                combination_hash VARCHAR(32) UNIQUE NOT NULL,
                material_md5s JSON NOT NULL,
                positions JSON NOT NULL,
                generation_time DATETIME NOT NULL,
                batch_id VARCHAR(50) NOT NULL,
                quality_score DECIMAL(5,3) DEFAULT 0.000,
                INDEX idx_combination_hash (combination_hash),
                INDEX idx_batch_id (batch_id),
                INDEX idx_generation_time (generation_time)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            print("✅ video_combinations表创建完成")

            # 素材使用统计表（移除外键约束以避免兼容性问题）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_usage_stats (
                md5_hash VARCHAR(32) PRIMARY KEY,
                total_usage_count INT DEFAULT 0,
                position_usage JSON,
                last_used_time DATETIME,
                first_used_time DATETIME,
                INDEX idx_usage_count (total_usage_count),
                INDEX idx_last_used (last_used_time)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            print("✅ material_usage_stats表创建完成")

            # 生成批次表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS generation_batches (
                batch_id VARCHAR(50) PRIMARY KEY,
                generation_time DATETIME NOT NULL,
                total_materials INT NOT NULL,
                generated_videos INT NOT NULL,
                algorithm_version VARCHAR(20) DEFAULT 'v1.0',
                optimization_mode VARCHAR(20) DEFAULT 'balanced',
                INDEX idx_generation_time (generation_time)
            ) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
            """)
            print("✅ generation_batches表创建完成")

            self.connection.commit()
            cursor.close()
            print("✅ 所有数据库表创建完成")

        except Exception as e:
            print(f"❌ 数据库表创建失败: {e}")
            if 'cursor' in locals():
                cursor.close()
            raise
    
    def calculate_file_md5(self, file_path: str) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"❌ 计算MD5失败 {file_path}: {e}")
            return ""
    
    def get_or_create_material_md5(self, file_path: str) -> str:
        """获取或创建素材的MD5记录"""
        if not self.is_connected or not self.connection:
            return ""
        cursor = self.connection.cursor()
        
        # 先检查路径是否已存在
        cursor.execute("SELECT md5_hash FROM materials WHERE file_path = %s", (file_path,))
        result = cursor.fetchone()
        
        if result:
            # 更新最后见到时间
            cursor.execute(
                "UPDATE materials SET last_seen = %s WHERE md5_hash = %s",
                (datetime.now(), result[0])
            )
            self.connection.commit()
            cursor.close()
            return result[0]
        
        # 计算MD5
        md5_hash = self.calculate_file_md5(file_path)
        if not md5_hash:
            cursor.close()
            return ""
        
        # 检查MD5是否已存在（可能是重命名的文件）
        cursor.execute("SELECT file_path FROM materials WHERE md5_hash = %s", (md5_hash,))
        existing = cursor.fetchone()
        
        if existing:
            # 更新路径和时间
            cursor.execute(
                "UPDATE materials SET file_path = %s, file_name = %s, last_seen = %s WHERE md5_hash = %s",
                (file_path, os.path.basename(file_path), datetime.now(), md5_hash)
            )
        else:
            # 新文件，插入记录
            file_size = os.path.getsize(file_path)
            now = datetime.now()
            
            cursor.execute("""
                INSERT INTO materials (md5_hash, file_name, file_path, file_size, duration, first_seen, last_seen)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (md5_hash, os.path.basename(file_path), file_path, file_size, 0.0, now, now))
        
        self.connection.commit()
        cursor.close()
        return md5_hash
    
    def generate_combination_hash(self, material_md5s: List[str], positions: List[int]) -> str:
        """生成组合的唯一标识"""
        combination_str = "|".join([f"{md5}@{pos}" for md5, pos in zip(material_md5s, positions)])
        return hashlib.md5(combination_str.encode()).hexdigest()

    def is_combination_used(self, material_md5s: List[str], positions: List[int]) -> bool:
        """检查组合是否已经使用过"""
        if not self.is_connected or not self.connection:
            return False
        combo_hash = self.generate_combination_hash(material_md5s, positions)
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM video_combinations WHERE combination_hash = %s", (combo_hash,))
        result = cursor.fetchone()
        cursor.close()
        return result is not None

    def record_combination(self, material_md5s: List[str], positions: List[int],
                          batch_id: str, quality_score: float = 0.0) -> bool:
        """记录新的组合"""
        if not self.is_connected or not self.connection:
            return False
        combo_hash = self.generate_combination_hash(material_md5s, positions)

        cursor = self.connection.cursor()
        try:
            # 先确保所有MD5都在materials表中存在
            for md5_hash in material_md5s:
                cursor.execute("SELECT md5_hash FROM materials WHERE md5_hash = %s", (md5_hash,))
                if not cursor.fetchone():
                    print(f"⚠️ MD5 {md5_hash} 不存在于materials表中，跳过记录")
                    cursor.close()
                    return False

            cursor.execute("""
                INSERT INTO video_combinations
                (combination_hash, material_md5s, positions, generation_time, batch_id, quality_score)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (combo_hash, json.dumps(material_md5s), json.dumps(positions),
                  datetime.now(), batch_id, quality_score))

            # 更新素材使用统计
            self._update_usage_stats(material_md5s, positions)

            self.connection.commit()
            cursor.close()
            return True
        except mysql.connector.Error as e:
            print(f"❌ 记录组合失败: {e}")
            self.connection.rollback()
            cursor.close()
            return False

    def _update_usage_stats(self, material_md5s: List[str], positions: List[int]):
        """更新素材使用统计"""
        cursor = self.connection.cursor()

        for md5_hash, position in zip(material_md5s, positions):
            # 检查是否已有统计记录
            cursor.execute("SELECT position_usage, total_usage_count FROM material_usage_stats WHERE md5_hash = %s", (md5_hash,))
            result = cursor.fetchone()

            now = datetime.now()

            if result:
                # 更新现有记录
                position_usage = json.loads(result[0]) if result[0] else {}
                position_usage[str(position)] = position_usage.get(str(position), 0) + 1
                new_total = result[1] + 1

                cursor.execute("""
                    UPDATE material_usage_stats
                    SET total_usage_count = %s, position_usage = %s, last_used_time = %s
                    WHERE md5_hash = %s
                """, (new_total, json.dumps(position_usage), now, md5_hash))
            else:
                # 创建新记录
                position_usage = {str(position): 1}
                cursor.execute("""
                    INSERT INTO material_usage_stats
                    (md5_hash, total_usage_count, position_usage, last_used_time, first_used_time)
                    VALUES (%s, %s, %s, %s, %s)
                """, (md5_hash, 1, json.dumps(position_usage), now, now))

    def get_generation_stats(self) -> GenerationStats:
        """获取生成统计信息"""
        if not self.is_connected or not self.connection:
            return GenerationStats(0, 0, 0, 0)
        cursor = self.connection.cursor()

        # 总视频数
        cursor.execute("SELECT COUNT(*) FROM video_combinations")
        total_videos = cursor.fetchone()[0]

        # 总素材数
        cursor.execute("SELECT COUNT(*) FROM materials")
        total_materials = cursor.fetchone()[0]

        # 总批次数
        cursor.execute("SELECT COUNT(*) FROM generation_batches")
        total_batches = cursor.fetchone()[0]

        cursor.close()

        return GenerationStats(
            total_videos=total_videos,
            total_materials=total_materials,
            total_batches=total_batches,
            total_combinations=total_videos
        )

    def get_material_usage_ranking(self, limit: int = 50) -> List[Tuple[str, str, int, datetime, str]]:
        """获取素材使用排行"""
        if not self.is_connected or not self.connection:
            return []
        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT m.file_name, m.file_path,
                   COALESCE(s.total_usage_count, 0) as usage_count,
                   COALESCE(s.last_used_time, m.first_seen) as last_used,
                   m.md5_hash
            FROM materials m
            LEFT JOIN material_usage_stats s ON m.md5_hash = s.md5_hash
            ORDER BY usage_count DESC, last_used DESC
            LIMIT %s
        """, (limit,))

        results = cursor.fetchall()
        cursor.close()
        return results

    def create_batch_record(self, batch_id: str, total_materials: int,
                           generated_videos: int, optimization_mode: str = "balanced"):
        """创建批次记录"""
        if not self.is_connected or not self.connection:
            return
        cursor = self.connection.cursor()
        cursor.execute("""
            INSERT INTO generation_batches
            (batch_id, generation_time, total_materials, generated_videos, optimization_mode)
            VALUES (%s, %s, %s, %s, %s)
        """, (batch_id, datetime.now(), total_materials, generated_videos, optimization_mode))
        self.connection.commit()
        cursor.close()

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")
