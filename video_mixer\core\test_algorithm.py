#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法 - 基于原增强算法的改进版本
Test Algorithm - Enhanced version based on Original Enhanced Algorithm
"""

import random
import numpy as np
import uuid
import os
import sys
from datetime import datetime
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass

try:
    from .original_enhanced_algorithm import OriginalEnhancedVideoGenerator, GeneratedVideo, VideoSegment
    from .material_usage_manager import MaterialUsageManager
    from ..utils.logger import get_logger
except ImportError:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.original_enhanced_algorithm import OriginalEnhancedVideoGenerator, GeneratedVideo, VideoSegment
    from core.material_usage_manager import MaterialUsageManager
    from utils.logger import get_logger

class TestVideoGenerator(OriginalEnhancedVideoGenerator):
    """测试算法视频生成器 - 集成素材使用均衡策略"""
    
    def __init__(self, num_materials: int = 100, segments_per_video: int = 4, 
                 enable_similarity_check: bool = False, optimization_mode: str = "balanced",
                 debug_mode: bool = False):
        super().__init__(num_materials, segments_per_video, enable_similarity_check, optimization_mode)
        
        # 素材使用管理器
        self.usage_manager = MaterialUsageManager(debug_mode=debug_mode)
        self.debug_mode = debug_mode
        
        # 算法统计信息
        self.algorithm_stats = {
            'total_attempts': 0,
            'successful_generations': 0,
            'failed_generations': 0,
            'success_rate_history': [],
            'material_balance_history': []
        }
        
        self.logger = get_logger()
        self.logger.info("🧪 测试算法初始化完成")
        if debug_mode:
            self.logger.info("🔍 调试模式已启用")
    
    def set_materials(self, materials: List[any]) -> None:
        """设置素材列表并注册到使用管理器"""
        # 直接设置素材，不调用父类方法（因为父类可能没有这个方法）
        self.materials = materials

        # 注册素材到使用管理器
        self.usage_manager.register_materials(materials)

        self.logger.info(f"🎬 已设置 {len(materials)} 个素材")
        if self.debug_mode:
            self.logger.debug(f"素材列表: {[getattr(m, 'path', str(m))[:50] + '...' for m in materials[:5]]}")
    
    def generate_optimal_videos_test(self, max_videos: int) -> Tuple[int, List[GeneratedVideo]]:
        """
        测试算法的主要生成方法
        基于原增强算法，集成素材使用均衡策略
        """
        self.logger.info(f"🧪 开始测试算法生成，目标: {max_videos}个视频")

        # 检查素材是否已设置
        if not hasattr(self, 'materials') or not self.materials:
            self.logger.error("❌ 素材未设置，无法生成视频")
            return 0, []

        # 重置父类状态（重要！）
        self.reset()

        generated_videos = []
        attempts = 0
        max_attempts = max_videos * 50  # 最大尝试次数
        consecutive_failures = 0
        max_consecutive_failures = 20

        # 重置统计信息
        self.algorithm_stats['total_attempts'] = 0
        self.algorithm_stats['successful_generations'] = 0
        self.algorithm_stats['failed_generations'] = 0
        
        while len(generated_videos) < max_videos and attempts < max_attempts:
            attempts += 1
            self.algorithm_stats['total_attempts'] += 1
            
            try:
                # 使用智能素材选择生成视频
                video = self._generate_single_video_with_smart_selection()
                
                if video and self._is_video_valid(video, generated_videos):
                    generated_videos.append(video)
                    consecutive_failures = 0
                    self.algorithm_stats['successful_generations'] += 1
                    
                    # 更新素材使用统计 - 根据video_index获取实际素材
                    selected_materials = []
                    for seg in video.segments:
                        if hasattr(self, 'materials') and self.materials and seg.video_index < len(self.materials):
                            selected_materials.append(self.materials[seg.video_index])

                    if selected_materials:
                        self.usage_manager.update_usage(selected_materials)
                    
                    # 记录成功率
                    current_success_rate = len(generated_videos) / attempts * 100
                    self.algorithm_stats['success_rate_history'].append(current_success_rate)
                    
                    if self.debug_mode or len(generated_videos) % 10 == 0:
                        self.logger.info(f"✅ 已生成 {len(generated_videos)}/{max_videos} 个视频 "
                                       f"(成功率: {current_success_rate:.1f}%)")
                        
                        # 输出素材使用统计
                        usage_stats = self.usage_manager.get_usage_distribution_text()
                        self.logger.info(f"📊 {usage_stats}")
                
                else:
                    consecutive_failures += 1
                    self.algorithm_stats['failed_generations'] += 1
                    
                    if consecutive_failures >= max_consecutive_failures:
                        self.logger.warning(f"⚠️ 连续失败 {consecutive_failures} 次，可能需要调整参数")
                        break
            
            except Exception as e:
                self.logger.error(f"生成视频时发生异常: {e}")
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    break
        
        # 生成完成统计
        final_success_rate = len(generated_videos) / attempts * 100 if attempts > 0 else 0
        self.logger.info(f"🎉 测试算法生成完成:")
        self.logger.info(f"  成功生成: {len(generated_videos)}/{max_videos} 个视频")
        self.logger.info(f"  总尝试次数: {attempts}")
        self.logger.info(f"  整体成功率: {final_success_rate:.2f}%")
        
        # 输出最终素材使用统计
        final_stats = self.usage_manager.get_usage_statistics()
        if final_stats:
            self.logger.info(f"📊 最终素材统计:")
            self.logger.info(f"  使用分布: 最多{final_stats['max_usage']}次, "
                           f"最少{final_stats['min_usage']}次, "
                           f"平均{final_stats['avg_usage']:.1f}次")
            self.logger.info(f"  均衡度: 标准差{final_stats['usage_std']:.2f}, "
                           f"未使用{final_stats['unused_materials']}个")
        
        return len(generated_videos), generated_videos
    
    def _generate_single_video_with_smart_selection(self) -> Optional[GeneratedVideo]:
        """使用智能素材选择生成单个视频"""
        if not self.materials:
            return None
        
        try:
            # 使用素材使用管理器智能选择素材
            selected_materials = self.usage_manager.select_materials(
                self.materials, 
                self.segments_per_video
            )
            
            if len(selected_materials) < self.segments_per_video:
                if self.debug_mode:
                    self.logger.debug(f"选择的素材数量不足: {len(selected_materials)}/{self.segments_per_video}")
                return None
            
            # 为每个选中的素材生成视频片段
            segments = []
            for i, material in enumerate(selected_materials):
                segment = self._create_segment_from_material(material, i)
                if segment:
                    segments.append(segment)
                elif self.debug_mode:
                    self.logger.debug(f"片段创建失败: 素材{getattr(material, 'path', str(material))}")

            if len(segments) < self.segments_per_video:
                if self.debug_mode:
                    self.logger.debug(f"生成的片段数量不足: {len(segments)}/{self.segments_per_video}")
                return None
            
            # 创建生成的视频对象
            video = GeneratedVideo(
                segments=segments,
                video_id=str(uuid.uuid4())
            )
            
            return video
            
        except Exception as e:
            if self.debug_mode:
                self.logger.debug(f"生成单个视频时异常: {e}")
            return None
    
    def _create_segment_from_material(self, material: any, segment_index: int) -> Optional[VideoSegment]:
        """从素材创建视频片段"""
        try:
            # 获取素材基本信息
            material_path = getattr(material, 'path', str(material))
            material_duration = getattr(material, 'duration', 30.0)  # 默认30秒
            
            # 生成随机的片段参数
            segment_duration = random.uniform(2.0, min(8.0, material_duration * 0.8))
            max_start_time = max(0, material_duration - segment_duration)
            start_time = random.uniform(0, max_start_time) if max_start_time > 0 else 0
            
            # 生成其他随机参数
            speed_factor = random.uniform(0.8, 1.2)
            
            # 获取素材在列表中的索引
            material_index = 0
            if hasattr(self, 'materials') and self.materials:
                try:
                    material_index = self.materials.index(material)
                except ValueError:
                    material_index = segment_index

            segment = VideoSegment(
                video_index=material_index,
                position=segment_index,
                start_time=start_time,
                duration=segment_duration
            )
            
            return segment
            
        except Exception as e:
            if self.debug_mode:
                self.logger.debug(f"创建片段失败: {e}")
            return None
    
    def _calculate_quality_score(self, segments: List[VideoSegment]) -> float:
        """计算视频质量分数"""
        try:
            # 基础分数
            base_score = 0.7
            
            # 时长合理性加分
            total_duration = sum(seg.duration for seg in segments)
            if 10 <= total_duration <= 30:
                base_score += 0.1
            
            # 素材多样性加分
            unique_materials = len(set(getattr(seg.material, 'path', str(seg.material)) for seg in segments))
            if unique_materials == len(segments):  # 所有素材都不重复
                base_score += 0.2
            
            return min(1.0, base_score)
            
        except Exception:
            return 0.5
    
    def _is_video_valid(self, video: GeneratedVideo, existing_videos: List[GeneratedVideo]) -> bool:
        """检查视频是否有效（使用原增强算法的严格检测逻辑）"""
        try:
            # 基本检查：确保视频有足够的片段
            if not video or not video.segments or len(video.segments) < self.segments_per_video:
                if self.debug_mode:
                    self.logger.debug(f"视频片段数量不足: {len(video.segments) if video and video.segments else 0}/{self.segments_per_video}")
                return False

            # 使用父类的原增强算法检测逻辑
            return self.is_video_valid_original_enhanced(video, existing_videos)

        except Exception as e:
            if self.debug_mode:
                self.logger.debug(f"视频有效性检查异常: {e}")
            return False
    
    def get_algorithm_statistics(self) -> Dict[str, any]:
        """获取算法统计信息"""
        stats = self.algorithm_stats.copy()
        stats['usage_manager_stats'] = self.usage_manager.get_usage_statistics()
        stats['material_usage_details'] = self.usage_manager.get_material_usage_details()
        
        return stats
    
    def reset_algorithm_state(self) -> None:
        """重置算法状态"""
        self.usage_manager.reset_statistics()
        self.algorithm_stats = {
            'total_attempts': 0,
            'successful_generations': 0,
            'failed_generations': 0,
            'success_rate_history': [],
            'material_balance_history': []
        }
        
        self.logger.info("🔄 测试算法状态已重置")
    
    def set_debug_mode(self, debug_mode: bool) -> None:
        """设置调试模式"""
        self.debug_mode = debug_mode
        self.usage_manager.debug_mode = debug_mode
        
        if debug_mode:
            self.logger.info("🔍 测试算法调试模式已启用")
        else:
            self.logger.info("🔍 测试算法调试模式已关闭")
